"use client";
import type { InnerItem } from "@/types";

type QueryParams = Record<string, string | boolean | number>;

const getBreadCrumbItems = (
  screenName: string,
  query: QueryParams = {}
): InnerItem[] => {
  const breadcrumbItems = [
    {
      screen_name: "Home",
      child_screens: [
        { name: "Home", path: "/pages/dashboard", isQuery: false },
      ],
    },
    {
      screen_name: "Course Details",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
      ],
    },
    {
      screen_name: "Subject Details",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },

        {
          name: "Course Details",
          path: "/pages/section-details",
          isQuery: true,
          clickable: false,
        },
        {
          name: "Subject Details",
          path: "/pages/subject-details",
          isQuery: true,
          clickable: false,
        },
      ],
    },
    {
      screen_name: "Video Player",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
        {
          name: "Resource Viewer",
          path: "/pages/video-player",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "Video Player Topicwise",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path: "/pages/course-details",
          isQuery: true,
          clickable: true,
        },
        {
          name: "Resource Details",
          path: "/pages/course-resource",
          isQuery: true,
          clickable: true,
        },
        {
          name: "Resource Viewer",
          path: "/pages/video-player",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "Document Viewer",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
        {
          name: "Resource Viewer",
          path: "/pages/document-viewer",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "Document Viewer Topicwise",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path: "/pages/course-details",
          isQuery: true,
          clickable: true,
        },
        {
          name: "Resource Details",
          path: "/pages/course-resource",
          isQuery: true,
          clickable: true,
        },
        {
          name: "Resource Viewer",
          path: "/pages/document-viewer",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "Image Viewer",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
        {
          name: "Image Viewer",
          path: "/pages/document-viewer",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "Pdf Viewer",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
        {
          name: "Pdf Viewer",
          path: "/pages/pdf-viewer",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "Pdf Viewer Topicwise",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path: "/pages/course-details",
          isQuery: true,
          clickable: true,
        },
        {
          name: "Resource Details",
          path: "/pages/course-resource",
          isQuery: true,
          clickable: true,
        },
        {
          name: "Pdf Viewer",
          path: "/pages/pdf-viewer",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "Exam List",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
          clickable: true,
        },
        { name: "Exam List", path: "/pages/exams-list", isQuery: false },
      ],
    },
    {
      screen_name: "Exam Intro",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
        { name: "Exam List", path: "/pages/exams-list", isQuery: true },
        { name: "Exam Intro", path: "/pages/examsIntro", isQuery: true },
      ],
    },
    {
      screen_name: "Exam View",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: false,
        },
        { name: "Course Details", path: "/pages/exam-view", isQuery: true },
        { name: "Exam List", path: "/pages/exam-view", isQuery: true },
        {
          name: "Exam Intro",
          path: "/pages/exam-view",
          isQuery: true,
          clickable: false,
        },
        {
          name: "Exam View",
          path: "/pages/exam-view",
          isQuery: false,
          clickable: false,
        },
      ],
    },
    {
      screen_name: "Video Checkpoint Exam View",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: false,
        },
        {
          name: "Course Details",
          path: "/pages/exam-view",
          isQuery: true,
          clickable: false,
        },
        {
          name: "Resource Viewer",
          path: "/pages/video-player",
          isQuery: false,
          clickable: false,
        },
        {
          name: "Exam View",
          path: "/pages/exam-view",
          isQuery: false,
          clickable: false,
        },
      ],
    },
    {
      screen_name: "Exam Review",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
        { name: "Exam List", path: "/pages/exams-list", isQuery: true },
        {
          name: "Exam Intro",
          path: "/pages/exam-review",
          isQuery: true,
          clickable: false,
        },
        {
          name: "Exam View",
          path: "/pages/exam-review",
          isQuery: true,
          clickable: false,
        },
        { name: "Exam Review", path: "/pages/exam-review", isQuery: false },
      ],
    },
    {
      screen_name: "Result Analysis",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
        { name: "Exam List", path: "/pages/exams-list", isQuery: true },
        {
          name: "Exam Intro",
          path: "/pages/examsIntro",
          isQuery: true,
          clickable: false,
        },
        {
          name: "Exam View",
          path: "/pages/exam-view",
          isQuery: true,
          clickable: false,
        },
        { name: "Exam Review", path: "/pages/exam-review", isQuery: true },
        {
          name: "Exam Result Analysis",
          path: "/pages/examResultAnalysis",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "View Result",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
        { name: "Exam List", path: "/pages/exam-list", isQuery: true },
        {
          name: "Exam Intro",
          path: "/pages/exam-view",
          isQuery: true,
          clickable: false,
        },
        {
          name: "Exam View",
          path: "/pages/exam-view",
          isQuery: false,
          clickable: false,
        },
      ],
    },
    {
      screen_name: "Result Analysis View",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
        { name: "Exam List", path: "/pages/exams-list", isQuery: true },
        {
          name: "Exam Result Analysis",
          path: "/pages/examResultAnalysis",
          isQuery: false,
        },
      ],
    },
    ,
    {
      screen_name: "Profile",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        { name: "Profile", path: "/pages/profile", isQuery: false },
      ],
    },
    {
      screen_name: "Course Rank List",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        { name: "Rank List", path: "/pages/course-rank-list", isQuery: false },
      ],
    },
    {
      screen_name: "Exam History",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        { name: "Exam History", path: "/pages/exam-history", isQuery: false },
      ],
    },
    {
      screen_name: "Subscription",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        { name: "Subscription", path: "/pages/subscriptions", isQuery: false },
      ],
    },
    {
      screen_name: "Exam Rank List",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
        { name: "Exam Rank List", path: "/exam-rank-list", isQuery: false },
      ],
    },
    {
      screen_name: "Pdf Viewer",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path: "/pages/course-details",
          isQuery: true,
        },

        {
          name: "Pdf Viewer",
          path: "/pages/pdf-viewer",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "Current Affairs",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Current Affairs",
          path: "/pages/current-affairs",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "Current Affairs Details",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Current Affairs",
          path: "/pages/current-affairs",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Current Affairs Details",
          path: "/pages/current-affairs",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "HTML Viewer",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
        {
          name: "HTML Viewer",
          path: "/pages/html-viewer",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "HTML Viewer Topicwise",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path: "/pages/course-details",
          isQuery: true,
          clickable: true,
        },
        {
          name: "Resource Details",
          path: "/pages/course-resource",
          isQuery: true,
          clickable: true,
        },
        {
          name: "HTML Viewer",
          path: "/pages/html-viewer",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "Image Viewer",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path:
            process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true"
              ? "/pages/section-details"
              : "/pages/course-details",
          isQuery: true,
        },
        {
          name: "Image Viewer",
          path: "/pages/html-viewer",
          isQuery: false,
        },
      ],
    },
    {
      screen_name: "Image Viewer Topicwise",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Course Details",
          path: "/pages/course-details",
          isQuery: true,
          clickable: true,
        },
        {
          name: "Resource Details",
          path: "/pages/course-resource",
          isQuery: true,
          clickable: true,
        },
        {
          name: "Image Viewer",
          path: "/pages/html-viewer",
          isQuery: false,
        },
      ],
    },
     {
      screen_name: "Notification",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        { name: "Notification", path: "/pages/notifications", isQuery: false },
      ],
    },
    {
      screen_name: "Courses",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Courses",
          path: "/pages/courses",
          isQuery: true,
        },
      ],
    },
    {
      screen_name: "Join Meeting",
      child_screens: [
        {
          name: "Home",
          path: "/pages/dashboard",
          isQuery: false,
          clickable: true,
        },
        {
          name: "Join Meeting",
          path: "/pages/join-meet",
          isQuery: true,
        },
      ],
    },
  ];

  const breadcrumb = breadcrumbItems.find(
    (item) => item?.screen_name === screenName
  );

  if (breadcrumb) {
    breadcrumb.child_screens.forEach((item) => {
      if (item.isQuery && Object.keys(query).length > 0) {
        const params = new URLSearchParams(
          Object.entries(query).map(([key, value]) => [key, String(value)])
        );
        item.path = `${item.path}?${params.toString()}`;
      }
    });
    return [...breadcrumb.child_screens];
  } else {
    return [];
  }
};

export default getBreadCrumbItems;
