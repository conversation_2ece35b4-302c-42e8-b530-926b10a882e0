"use client";

import React, { useRef, useEffect, useState } from 'react';

interface HTML5VideoPlayerProps {
  src: string;
  onProgress?: (data: { played: number; playedSeconds: number; loaded: number; loadedSeconds: number }) => void;
  onStart?: () => void;
  onEnd?: () => void;
  onReady?: () => void;
  width?: string;
  height?: string;
  className?: string;
}

const HTML5VideoPlayer: React.FC<HTML5VideoPlayerProps> = ({
  src,
  onProgress,
  onStart,
  onEnd,
  onReady,
  width = "100%",
  height = "500px",
  className = ""
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedMetadata = () => {
      setDuration(video.duration);
      onReady?.();
    };

    const handlePlay = () => {
      setIsPlaying(true);
      onStart?.();
    };

    const handlePause = () => {
      setIsPlaying(false);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      onEnd?.();
    };

    const handleTimeUpdate = () => {
      const current = video.currentTime;
      const total = video.duration;
      
      setCurrentTime(current);
      
      if (total > 0) {
        onProgress?.({
          played: current / total,
          playedSeconds: current,
          loaded: 1,
          loadedSeconds: total
        });
      }
    };

    // Add event listeners
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('ended', handleEnded);
    video.addEventListener('timeupdate', handleTimeUpdate);

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('ended', handleEnded);
      video.removeEventListener('timeupdate', handleTimeUpdate);
    };
  }, [onProgress, onStart, onEnd, onReady]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`relative ${className}`} style={{ width, height }}>
      <video
        ref={videoRef}
        src={src}
        controls
        width="100%"
        height="100%"
        className="rounded-lg"
        crossOrigin="anonymous"
      />
      
      {/* Custom overlay with additional info */}
      <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
        {formatTime(currentTime)} / {formatTime(duration)}
      </div>
      
      {/* Play/Pause indicator */}
      {isPlaying && (
        <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-sm">
          ▶ Playing
        </div>
      )}
    </div>
  );
};

export default HTML5VideoPlayer;
