"use client";

import React, { useEffect, useRef } from 'react';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

interface VideoJsPlayerProps {
  src: string;
  onProgress?: (data: { played: number; playedSeconds: number; loaded: number; loadedSeconds: number }) => void;
  onStart?: () => void;
  onEnd?: () => void;
  onReady?: () => void;
  width?: string;
  height?: string;
  className?: string;
}

const VideoJsPlayer: React.FC<VideoJsPlayerProps> = ({
  src,
  onProgress,
  onStart,
  onEnd,
  onReady,
  width = "100%",
  height = "500px",
  className = ""
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);

  useEffect(() => {
    if (videoRef.current && !playerRef.current) {
      // Initialize Video.js player
      playerRef.current = videojs(videoRef.current, {
        controls: true,
        responsive: true,
        fluid: true,
        sources: [{
          src: src,
          type: 'video/mp4'
        }]
      });

      // Event listeners
      playerRef.current.on('loadedmetadata', () => {
        onReady?.();
      });

      playerRef.current.on('play', () => {
        onStart?.();
      });

      playerRef.current.on('ended', () => {
        onEnd?.();
      });

      playerRef.current.on('timeupdate', () => {
        const currentTime = playerRef.current.currentTime();
        const duration = playerRef.current.duration();
        
        if (duration > 0) {
          onProgress?.({
            played: currentTime / duration,
            playedSeconds: currentTime,
            loaded: 1,
            loadedSeconds: duration
          });
        }
      });
    }

    return () => {
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }
    };
  }, [src, onProgress, onStart, onEnd, onReady]);

  return (
    <div className={className} style={{ width, height }}>
      <video
        ref={videoRef}
        className="video-js vjs-default-skin"
        controls
        preload="auto"
        width={width}
        height={height}
        data-setup="{}"
      />
    </div>
  );
};

export default VideoJsPlayer;
