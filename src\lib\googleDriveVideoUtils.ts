/**
 * Google Drive video URL utilities for video players
 */

/**
 * Extract file ID from Google Drive URL
 * @param url - Google Drive URL
 * @returns File ID or null if not found
 */
export function extractGoogleDriveFileId(url: string): string | null {
  const patterns = [
    /\/file\/d\/([a-zA-Z0-9-_]+)/,                    // /file/d/FILE_ID
    /id=([a-zA-Z0-9-_]+)/,                            // ?id=FILE_ID
    /\/open\?id=([a-zA-Z0-9-_]+)/,                    // /open?id=FILE_ID
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return match[1];
    }
  }
  
  return null;
}

/**
 * Convert Google Drive URL to playable video URL
 * @param url - Google Drive URL
 * @returns Playable video URL
 */
export function convertGoogleDriveVideoUrl(url: string): string | null {
  const fileId = extractGoogleDriveFileId(url);
  
  if (!fileId) {
    console.warn('Could not extract file ID from Google Drive URL:', url);
    return null;
  }

  // Return direct streaming URL that works with video players
  return `https://drive.google.com/uc?export=download&id=${fileId}`;
}

/**
 * Get multiple video URL formats for Google Drive
 * @param url - Google Drive URL
 * @returns Object with different URL formats
 */
export function getGoogleDriveVideoUrls(url: string) {
  const fileId = extractGoogleDriveFileId(url);
  
  if (!fileId) {
    return null;
  }

  return {
    fileId,
    originalUrl: url,
    // Direct download - best for video players
    directStream: `https://drive.google.com/uc?export=download&id=${fileId}`,
    // Alternative streaming URLs
    streamUrl1: `https://drive.google.com/file/d/${fileId}/preview`,
    streamUrl2: `https://docs.google.com/uc?export=download&id=${fileId}`,
    // Embed URL (for iframe)
    embedUrl: `https://drive.google.com/file/d/${fileId}/preview`,
    // Thumbnail
    thumbnail: `https://drive.google.com/thumbnail?id=${fileId}&sz=w400-h300`
  };
}

/**
 * Check if URL is a Google Drive URL
 * @param url - URL to check
 * @returns Boolean indicating if it's a Google Drive URL
 */
export function isGoogleDriveUrl(url: string): boolean {
  return url.includes('drive.google.com') || url.includes('docs.google.com');
}

/**
 * Process video URL for playback - converts Google Drive URLs to playable format
 * @param url - Original video URL
 * @returns Processed URL suitable for video playback
 */
export function processVideoUrl(url: string): string {
  if (!url) return url;
  
  if (isGoogleDriveUrl(url)) {
    const convertedUrl = convertGoogleDriveVideoUrl(url);
    if (convertedUrl) {
      console.log('🔄 Converted Google Drive video URL:', convertedUrl);
      return convertedUrl;
    } else {
      console.warn('⚠️ Failed to convert Google Drive URL, using original:', url);
    }
  }
  
  return url; // Return original URL if not Google Drive or conversion failed
}

/**
 * Get best video URL for ReactPlayer
 * @param url - Original video URL
 * @returns Best URL for video playback
 */
export function getBestVideoUrl(url: string): string {
  if (!url) return url;
  
  if (isGoogleDriveUrl(url)) {
    const urls = getGoogleDriveVideoUrls(url);
    if (urls) {
      // Try different URLs in order of preference
      const candidates = [
        urls.directStream,
        urls.streamUrl2,
        urls.streamUrl1,
        urls.embedUrl
      ];
      
      console.log('🎬 Available video URLs for Google Drive:', candidates);
      
      // Return the first (most likely to work) URL
      return candidates[0];
    }
  }
  
  return url;
}

/**
 * Test function for Google Drive video URL conversion
 */
export function testGoogleDriveVideoUrl(testUrl: string) {
  console.log('🧪 Testing Google Drive video URL conversion...');
  console.log('Original URL:', testUrl);
  
  const urls = getGoogleDriveVideoUrls(testUrl);
  if (urls) {
    console.log('📁 File ID:', urls.fileId);
    console.log('🎬 Direct Stream:', urls.directStream);
    console.log('🎥 Stream URL 1:', urls.streamUrl1);
    console.log('🎦 Stream URL 2:', urls.streamUrl2);
    console.log('📺 Embed URL:', urls.embedUrl);
    console.log('🖼️ Thumbnail:', urls.thumbnail);
    
    console.log('\n🎯 Recommended for ReactPlayer:', urls.directStream);
    
    return urls;
  } else {
    console.log('❌ Failed to extract file ID');
    return null;
  }
}
