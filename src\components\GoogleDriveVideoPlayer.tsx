"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Play, Pause, RotateCcw, RotateCw } from 'lucide-react';

interface GoogleDriveVideoPlayerProps {
  src: string;
  width?: string;
  height?: string;
  onProgress?: (progress: { played: number; playedSeconds: number; loaded: number; loadedSeconds: number }) => void;
  onStart?: () => void;
  onEnd?: () => void;
  onReady?: () => void;
  totalDuration?: string; // Format: "HH:MM:SS"
  className?: string;
}

const GoogleDriveVideoPlayer: React.FC<GoogleDriveVideoPlayerProps> = ({
  src,
  width = "100%",
  height = "500px",
  onProgress,
  onStart,
  onEnd,
  onReady,
  totalDuration = "00:00:00",
  className = ""
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);
  const [hasEnded, setHasEnded] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [progress, setProgress] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number | null>(null);

  // Convert duration string to seconds
  const getDurationInSeconds = (duration: string): number => {
    const [hours, minutes, seconds] = duration.split(":").map(Number);
    return hours * 3600 + minutes * 60 + seconds;
  };

  const totalSeconds = getDurationInSeconds(totalDuration);

  // Handle play/start
  const handleStart = () => {
    if (!hasStarted) {
      setHasStarted(true);
      startTimeRef.current = Date.now();
      onStart?.();
      onReady?.();
    }
    
    setIsPlaying(true);
    
    // Start progress tracking
    if (intervalRef.current) clearInterval(intervalRef.current);
    
    intervalRef.current = setInterval(() => {
      if (startTimeRef.current) {
        const elapsed = (Date.now() - startTimeRef.current) / 1000;
        const newProgress = Math.min(elapsed / totalSeconds, 1);
        
        setCurrentTime(elapsed);
        setProgress(newProgress);
        
        onProgress?.({
          played: newProgress,
          playedSeconds: elapsed,
          loaded: 1,
          loadedSeconds: totalSeconds
        });
        
        // Auto-end when reaching total duration
        if (elapsed >= totalSeconds && !hasEnded) {
          handleEnd();
        }
      }
    }, 1000);
  };

  // Handle pause
  const handlePause = () => {
    setIsPlaying(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // Handle end
  const handleEnd = () => {
    setIsPlaying(false);
    setHasEnded(true);
    setProgress(1);
    setCurrentTime(totalSeconds);
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    
    onEnd?.();
  };

  // Handle skip
  const handleSkip = (seconds: number) => {
    if (startTimeRef.current) {
      const newTime = Math.max(0, Math.min(currentTime + seconds, totalSeconds));
      const timeDiff = newTime - currentTime;
      startTimeRef.current -= timeDiff * 1000; // Adjust start time
      setCurrentTime(newTime);
      setProgress(newTime / totalSeconds);
    }
  };

  // Format time for display
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <div className={`relative ${className}`}>
      {/* Google Drive iframe */}
      <iframe
        src={src}
        width={width}
        height={height}
        allow="autoplay; encrypted-media"
        allowFullScreen
        title="Google Drive Video"
        className="rounded-lg"
      />
      
      {/* Custom controls overlay */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
        {/* Progress bar */}
        <div className="mb-3">
          <div className="w-full bg-gray-600 rounded-full h-1">
            <div 
              className="bg-blue-500 h-1 rounded-full transition-all duration-300"
              style={{ width: `${progress * 100}%` }}
            />
          </div>
        </div>
        
        {/* Controls */}
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center gap-3">
            {/* Play/Pause button */}
            {!hasStarted ? (
              <button
                onClick={handleStart}
                className="bg-blue-600 hover:bg-blue-700 p-2 rounded-full transition-colors"
                title="Start Video"
              >
                <Play size={20} fill="white" />
              </button>
            ) : isPlaying ? (
              <button
                onClick={handlePause}
                className="bg-blue-600 hover:bg-blue-700 p-2 rounded-full transition-colors"
                title="Pause"
              >
                <Pause size={20} fill="white" />
              </button>
            ) : (
              <button
                onClick={handleStart}
                className="bg-blue-600 hover:bg-blue-700 p-2 rounded-full transition-colors"
                title="Resume"
                disabled={hasEnded}
              >
                <Play size={20} fill="white" />
              </button>
            )}
            
            {/* Skip buttons */}
            <button
              onClick={() => handleSkip(-10)}
              className="bg-gray-600 hover:bg-gray-700 p-1 rounded transition-colors"
              title="Skip back 10s"
              disabled={!hasStarted}
            >
              <RotateCcw size={16} />
            </button>
            
            <button
              onClick={() => handleSkip(10)}
              className="bg-gray-600 hover:bg-gray-700 p-1 rounded transition-colors"
              title="Skip forward 10s"
              disabled={!hasStarted || hasEnded}
            >
              <RotateCw size={16} />
            </button>
            
            {/* Complete button */}
            <button
              onClick={handleEnd}
              className="bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-sm transition-colors"
              disabled={!hasStarted || hasEnded}
            >
              {hasEnded ? "Completed" : "Mark Complete"}
            </button>
          </div>
          
          {/* Time display */}
          <div className="text-sm">
            {formatTime(currentTime)} / {formatTime(totalSeconds)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GoogleDriveVideoPlayer;
