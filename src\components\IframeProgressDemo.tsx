"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { isGoogleDriveUrl, getGoogleDriveVideoUrls } from '@/lib/googleDriveVideoUtils';
import { timeToSeconds } from '@/lib/timeUtils';

const IframeProgressDemo: React.FC = () => {
  const [testUrl, setTestUrl] = useState('');
  const [embedUrl, setEmbedUrl] = useState('');
  const [duration, setDuration] = useState('00:03:00'); // 3 minutes for demo
  const [hasStarted, setHasStarted] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [videoEnded, setVideoEnded] = useState(false);
  const [playbackPosition, setPlaybackPosition] = useState(0);
  const [progressInterval, setProgressInterval] = useState<NodeJS.Timeout | null>(null);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [events, setEvents] = useState<string[]>([]);

  const addEvent = (event: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setEvents(prev => [`[${timestamp}] ${event}`, ...prev.slice(0, 9)]);
  };

  const handleTest = () => {
    if (!testUrl.trim()) {
      alert('Please enter a Google Drive URL');
      return;
    }

    if (!isGoogleDriveUrl(testUrl)) {
      alert('Please enter a valid Google Drive URL');
      return;
    }

    const urls = getGoogleDriveVideoUrls(testUrl);
    if (urls) {
      setEmbedUrl(urls.embedUrl);
      addEvent(`Generated embed URL for iframe`);
      // Reset states
      resetVideoState();
    } else {
      alert('Failed to process Google Drive URL');
    }
  };

  const resetVideoState = () => {
    setHasStarted(false);
    setIsPlaying(false);
    setVideoEnded(false);
    setPlaybackPosition(0);
    setStartTime(null);
    if (progressInterval) {
      clearInterval(progressInterval);
      setProgressInterval(null);
    }
  };

  const startProgress = () => {
    if (progressInterval) return; // Already started
    
    setHasStarted(true);
    setIsPlaying(true);
    setStartTime(Date.now());
    addEvent('Progress tracking started');
    
    // Start progress simulation
    const interval = setInterval(() => {
      if (startTime) {
        const elapsed = (Date.now() - startTime) / 1000;
        const totalSecs = timeToSeconds(duration);
        const progress = Math.min(elapsed / totalSecs, 1);
        
        setPlaybackPosition(elapsed);
        
        // Auto complete when reaching end
        if (elapsed >= totalSecs) {
          completeVideo();
        }
      }
    }, 1000);
    
    setProgressInterval(interval);
  };

  const completeVideo = () => {
    if (progressInterval) {
      clearInterval(progressInterval);
      setProgressInterval(null);
    }
    
    setIsPlaying(false);
    setVideoEnded(true);
    setPlaybackPosition(timeToSeconds(duration));
    addEvent('Video marked as completed');
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (progressInterval) {
        clearInterval(progressInterval);
      }
    };
  }, [progressInterval]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progressPercentage = duration ? (playbackPosition / timeToSeconds(duration)) * 100 : 0;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🎬 Iframe Progress Tracking Demo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* URL Input */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Google Drive Video URL:
            </label>
            <input
              type="text"
              value={testUrl}
              onChange={(e) => setTestUrl(e.target.value)}
              placeholder="https://drive.google.com/file/d/YOUR_FILE_ID/view?usp=sharing"
              className="w-full p-3 border rounded-md"
            />
          </div>

          {/* Duration Input */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Video Duration (HH:MM:SS):
            </label>
            <input
              type="text"
              value={duration}
              onChange={(e) => setDuration(e.target.value)}
              placeholder="00:03:00"
              className="w-full p-3 border rounded-md"
            />
          </div>

          <button
            onClick={handleTest}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-md"
          >
            Load Video
          </button>

          {/* Video Player with Progress Overlay */}
          {embedUrl && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-4">Iframe with Progress Tracking:</h3>
              
              <div className="relative bg-black rounded-lg overflow-hidden" style={{ height: '400px' }}>
                {/* Google Drive iframe */}
                <iframe
                  src={embedUrl}
                  width="100%"
                  height="100%"
                  allow="autoplay; encrypted-media"
                  allowFullScreen
                  title="Google Drive Video Demo"
                  className="w-full h-full"
                />
                
                {/* Progress overlay */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent p-4">
                  {/* Progress bar */}
                  <div className="mb-3">
                    <div className="w-full bg-gray-600 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progressPercentage}%` }}
                      />
                    </div>
                  </div>
                  
                  {/* Controls */}
                  <div className="flex items-center justify-between text-white">
                    <div className="flex items-center gap-3">
                      {/* Start button */}
                      {!hasStarted && (
                        <button
                          onClick={startProgress}
                          className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-full flex items-center gap-2 transition-colors"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8 5v14l11-7z"/>
                          </svg>
                          Start Tracking
                        </button>
                      )}
                      
                      {/* Complete button */}
                      {hasStarted && !videoEnded && (
                        <button
                          onClick={completeVideo}
                          className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded transition-colors"
                        >
                          Mark Complete
                        </button>
                      )}
                      
                      {/* Status indicators */}
                      {hasStarted && isPlaying && !videoEnded && (
                        <span className="bg-blue-600 px-3 py-1 rounded text-sm flex items-center gap-1">
                          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                          Tracking...
                        </span>
                      )}
                      
                      {videoEnded && (
                        <span className="bg-green-600 px-3 py-1 rounded text-sm">
                          ✓ Completed
                        </span>
                      )}
                    </div>
                    
                    {/* Time display */}
                    <div className="text-sm">
                      {hasStarted ? (
                        <span>
                          {formatTime(playbackPosition)} / {duration}
                        </span>
                      ) : (
                        <span>Duration: {duration}</span>
                      )}
                    </div>
                  </div>
                  
                  {/* Progress percentage */}
                  {hasStarted && (
                    <div className="mt-2 text-center">
                      <span className="text-xs text-gray-300">
                        Progress: {Math.round(progressPercentage)}%
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Progress Stats */}
          {embedUrl && (
            <div className="bg-gray-50 p-4 rounded-md">
              <h4 className="font-semibold mb-2">Progress Stats:</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Status:</strong> {videoEnded ? 'Completed' : hasStarted ? 'In Progress' : 'Not Started'}
                </div>
                <div>
                  <strong>Progress:</strong> {Math.round(progressPercentage)}%
                </div>
                <div>
                  <strong>Current Time:</strong> {formatTime(playbackPosition)}
                </div>
                <div>
                  <strong>Total Duration:</strong> {duration}
                </div>
              </div>
            </div>
          )}

          {/* Event Log */}
          {events.length > 0 && (
            <div className="bg-gray-50 p-4 rounded-md">
              <h4 className="font-semibold mb-2">Event Log:</h4>
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {events.map((event, index) => (
                  <div key={index} className="text-sm font-mono text-gray-700">
                    {event}
                  </div>
                ))}
              </div>
              <button
                onClick={() => setEvents([])}
                className="mt-2 text-sm text-blue-600 hover:text-blue-800"
              >
                Clear Log
              </button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>📝 How It Works</CardTitle>
        </CardHeader>
        <CardContent className="text-sm space-y-2">
          <p><strong>✅ Progress Tracking for Iframe:</strong></p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>Timer-based progress simulation</li>
            <li>Visual progress bar overlay</li>
            <li>Start/Complete manual controls</li>
            <li>Real-time progress percentage</li>
            <li>Event logging for debugging</li>
          </ul>
          
          <p className="mt-4"><strong>🎯 Features:</strong></p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>Works with any Google Drive video URL</li>
            <li>Maintains existing ReactPlayer functionality</li>
            <li>Integrates with course progress system</li>
            <li>Automatic completion at video end</li>
            <li>Manual completion option</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default IframeProgressDemo;
