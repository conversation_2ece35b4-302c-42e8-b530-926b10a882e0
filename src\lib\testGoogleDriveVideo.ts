/**
 * Test Google Drive video URL conversion
 */

import { 
  getGoogleDriveVideoUrls, 
  processVideoUrl, 
  getBestVideoUrl,
  testGoogleDriveVideoUrl 
} from './googleDriveVideoUtils';

/**
 * Test Google Drive video URL with your specific URL
 */
export function testYourGoogleDriveVideo(videoUrl: string) {
  console.log('🎬 Testing Google Drive video URL...\n');
  console.log('Original URL:', videoUrl);
  
  const urls = getGoogleDriveVideoUrls(videoUrl);
  
  if (urls) {
    console.log('\n✅ Successfully extracted file ID:', urls.fileId);
    console.log('\n📋 Available video URL formats:');
    console.log('1. Direct Stream (recommended):', urls.directStream);
    console.log('2. Alternative Stream 1:', urls.streamUrl1);
    console.log('3. Alternative Stream 2:', urls.streamUrl2);
    console.log('4. Embed URL:', urls.embedUrl);
    console.log('5. Thumbnail:', urls.thumbnail);
    
    console.log('\n🎯 Best URL for ReactPlayer:', getBestVideoUrl(videoUrl));
    console.log('🔄 Processed URL:', processVideoUrl(videoUrl));
    
    return urls;
  } else {
    console.log('❌ Failed to process Google Drive video URL');
    return null;
  }
}

/**
 * Test multiple Google Drive video formats
 */
export function testMultipleGoogleDriveFormats() {
  const testUrls = [
    'https://drive.google.com/file/d/1ABC123/view?usp=sharing',
    'https://drive.google.com/file/d/1ABC123/view?usp=drive_link',
    'https://drive.google.com/open?id=1ABC123',
    'https://docs.google.com/file/d/1ABC123/edit'
  ];
  
  console.log('🧪 Testing multiple Google Drive URL formats...\n');
  
  testUrls.forEach((url, index) => {
    console.log(`\n--- Test ${index + 1} ---`);
    testYourGoogleDriveVideo(url);
  });
}

/**
 * Quick test function for debugging
 */
export function quickVideoTest(url: string) {
  console.log('🚀 Quick video URL test:');
  console.log('Input:', url);
  console.log('Output:', processVideoUrl(url));
  return processVideoUrl(url);
}
