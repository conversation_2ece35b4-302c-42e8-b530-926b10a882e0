"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { isGoogleDriveUrl, getGoogleDriveVideoUrls } from '@/lib/googleDriveVideoUtils';

const GoogleDriveVideoTest: React.FC = () => {
  const [testUrl, setTestUrl] = useState('');
  const [result, setResult] = useState<any>(null);

  const handleTest = () => {
    if (!testUrl.trim()) {
      alert('Please enter a Google Drive URL');
      return;
    }

    console.log('🧪 Testing Google Drive URL:', testUrl);
    
    const isGoogleDrive = isGoogleDriveUrl(testUrl);
    console.log('Is Google Drive URL:', isGoogleDrive);
    
    if (isGoogleDrive) {
      const urls = getGoogleDriveVideoUrls(testUrl);
      console.log('Generated URLs:', urls);
      setResult(urls);
    } else {
      setResult({ error: 'Not a Google Drive URL' });
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🎬 Google Drive Video URL Tester</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Google Drive Video URL:
            </label>
            <input
              type="text"
              value={testUrl}
              onChange={(e) => setTestUrl(e.target.value)}
              placeholder="https://drive.google.com/file/d/YOUR_FILE_ID/view?usp=sharing"
              className="w-full p-3 border rounded-md"
            />
          </div>
          
          <Button onClick={handleTest} className="w-full">
            Test URL
          </Button>

          {result && (
            <div className="mt-4 p-4 bg-gray-50 rounded-md">
              <h3 className="font-semibold mb-2">Result:</h3>
              {result.error ? (
                <p className="text-red-600">{result.error}</p>
              ) : (
                <div className="space-y-2 text-sm">
                  <p><strong>File ID:</strong> {result.fileId}</p>
                  <p><strong>Embed URL (for iframe):</strong></p>
                  <code className="block bg-white p-2 rounded text-xs break-all">
                    {result.embedUrl}
                  </code>
                  
                  <div className="mt-4">
                    <p><strong>Preview:</strong></p>
                    <iframe
                      src={result.embedUrl}
                      width="100%"
                      height="300"
                      frameBorder="0"
                      allow="autoplay; encrypted-media"
                      allowFullScreen
                      title="Google Drive Video Test"
                      className="rounded border"
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GoogleDriveVideoTest;
