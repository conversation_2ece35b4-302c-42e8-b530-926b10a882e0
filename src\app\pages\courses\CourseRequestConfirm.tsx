"use client";
import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "next-i18next";
import { CoursesParams } from "@/types";

interface CourseRequestConfirmProps {
  course: CoursesParams | null;
  onConfirm: () => void;
  onCancel: () => void;
}

const CourseRequestConfirm: React.FC<CourseRequestConfirmProps> = ({
  course,
  onConfirm,
  onCancel,
}) => {
  const { t } = useTranslation("common");

  if (!course) return null;
  return (
    <div className="text-center">
      {/* Header */}
      <div className="mb-2 text-center text-lg">
        {t("Do you want to request access to this course?")}
      </div>
      {/* Action Buttons */}
      <div className="flex justify-center items-center space-x-6 mt-4">
        <Button
          variant="outline"
          onClick={onCancel}
          className="primary w-24 h-10 rounded-3xl"
        >
          {t("No")}
        </Button>
        <Button onClick={onConfirm} className=" w-24 h-10 rounded-3xl">
          {t("Yes")}
        </Button>
      </div>
    </div>
  );
};

export default CourseRequestConfirm;
