"use client";
import React, { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import ReactPlayer from "react-player";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import MainLayout from "../layouts/mainLayout";
import CommentsSection from "@/components/comments/comments";
import { useExam } from "@/hooks/useExam";
import {
  CheckPoint,
  CheckpointResult,
  CommentsResponse,
  CourseData,
  CourseProgressRequestType,
  ErrorCatch,
  GetCourseProgressRequest,
  GetSessionReport,
  InnerItem,
  LoginUserData,
  ToastType,
} from "@/types";
import { Modal } from "@/components/ui/modal";
import CheckPointModal from "@/components/checkPointModal";
import { KEYS } from "@/lib/keys";
import { useToast } from "@/components/ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import {
  CommandIcon,
  MessageCircle,
  MessageCircleIcon,
  MessageSquareCodeIcon,
  MessageSquarePlus,
  Pause,
  Play,
  Settings,
  ThumbsUp,
  VideoIcon,
} from "lucide-react";
import { useTranslation } from "next-i18next";
import { Card } from "@/components/ui/card";
import CommentsModal from "@/components/comment-modal";
import useComments from "@/hooks/useComments";
import { useCourse } from "@/hooks/useCourse";
import { getLocalStorageItem } from "@/lib/utils";
import { Spinner } from "@/components/ui/progressiveLoder";
import { timeToSeconds } from "@/lib/timeUtils";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { processVideoUrl, getBestVideoUrl, isGoogleDriveUrl, getGoogleDriveVideoUrls } from "@/lib/googleDriveVideoUtils";
import "../../../styles/main.css";
import { extractVideoIdFromSearch, UUID } from "@/lib/constants";
import { UseLogClass } from "@/hooks/useLog";
interface ProgressState {
  played: number;
  playedSeconds: number;
  loaded: number;
  loadedSeconds: number;
}

const VideoPlayer = (): React.JSX.Element => {
  const { t } = useTranslation("common");
  const [activeTab, setActiveTab] = useState("video-view");
  const [url, setUrl] = useState<string>("");
  const searchParams = useSearchParams();
  const router = useRouter();
  const getResourceData = localStorage.getItem(KEYS.RESOURCE_DATA);
  const parsedData = getResourceData ? JSON.parse(getResourceData) : "";
  const fileName = parsedData.name;
  const fileUrl = parsedData.external_url;
  const thumbnailImage = parsedData.thumbnail_url;
  const totalLength = parsedData.length;
  const instance_id = parsedData.id;
  const length = parsedData.length;
  const videoName = parsedData.name;
  const topicName = parsedData.topic_name;
  const is_checkpoint_enabled = parsedData.is_checkpoint_enabled;
  const course_module_id = parsedData.course_module_id;
  const exam_result = searchParams?.get("result") ?? "";
  const sectionId = searchParams?.get("section_id");
  const courseId = searchParams?.get("course_id") as string;
  const topicWise = searchParams?.get("is_topic_wise");
  const [checkPoints, setcheckPointData] = useState<CheckPoint[]>([]);
  const [checkPointsLength, setcheckPointDataLength] = useState<CheckPoint[]>(
    []
  );
  const [checkPoint, setCheckPoint] = useState<CheckPoint | null>(null);
  const [isPlaying, setIsPlaying] = useState(true);
  const [checkpointReached, setCheckpointReached] = useState(false);
  const [currentCheckpoint, setCurrentCheckpoint] = useState("");
  const [playbackPosition, setPlaybackPosition] = useState(0);
  const { toast } = useToast() as unknown as ToastType;
  const { getCheckPointsdetails, startCheckPointQuiz } = useExam();
  const [videoEnded, setVideoEnded] = useState(false);
  const [openFeedback, setOpenFeedback] = useState(false);
  const [commentsData, setCommentsData] = useState<CommentsResponse[]>([]);
  const [commentCount, setCommentCount] = useState<number>(0);
  const [courseData, setCourseData] = useState<CourseData[]>([]);
  const [cancelCheckpoint, setCancelCheckpoint] = useState(false);
  const [timeSpent, setTimeSpent] = useState<string>("");
  const [disableProgress, setDisableProgress] = useState<boolean>(true);
  const { getComments, addComments } = useComments();
  const { updateCourseProgress, getCourseProgress, getSessionReport } =
    useCourse();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isCommentsLoding, setIsCommentsLoading] = useState<boolean>(true);
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const [btnClick, setBtnClick] = useState<boolean>(false);
  const [hasStarted, setHasStarted] = useState<boolean>(false);
  const playerRef = useRef<ReactPlayer>(null);
  const hasHandledOnReady = useRef(false);
  const [skipBack, setSkipBack] = useState(10);
  const [skipForward, setSkipForward] = useState(10);
  const [showComments, setShowComments] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [userId, setUserId] = useState<string>("");
  const [isLogged, setIsLogged] = useState(false);
  const { insertLogDetails } = UseLogClass();
  const [LikeData, setLikeData] = useState<CommentsResponse[]>([]);

  // Function to get Google Drive iframe URL
  const getGoogleDriveIframeUrl = (url: string): string => {
    const urls = getGoogleDriveVideoUrls(url);
    const iframeUrl = urls ? urls.embedUrl : url;
    console.log('🎬 Google Drive iframe URL:', iframeUrl);
    return iframeUrl;
  };

  useEffect(() => {
    const data = {
      fileName,
      fileUrl,
      instance_id,
      is_checkpoint_enabled,
      course_module_id,
      length,
    };

    localStorage.setItem("storedData", JSON.stringify(data));
    setIsLoading(false);
    if (is_checkpoint_enabled) {
      setIsLoading(true);
      getCheckPointsUser();
    }

    if (exam_result === "PASSED") {
      setIsPlaying(true);
      const lastPlaybackPositionForPassed = localStorage.getItem(
        "lastPlaybackPositionForPassed"
      );
      if (lastPlaybackPositionForPassed) {
        setPlaybackPosition(parseFloat(lastPlaybackPositionForPassed));
        localStorage.removeItem("lastPlaybackPositionForPassed");
        localStorage.removeItem("storedData");
      }
    } else if (exam_result === "FAILED") {
      setIsPlaying(true);
      const lastPlaybackPositionForFailed = localStorage.getItem(
        "lastPlaybackPositionForFailed"
      );
      if (lastPlaybackPositionForFailed) {
        setPlaybackPosition(parseFloat(lastPlaybackPositionForFailed));
      }
    }
  }, [exam_result, is_checkpoint_enabled]);

  // to restrict browser back
  useEffect(() => {
    const USER_DATA = localStorage.getItem(KEYS.USER_DETAILS);
    if (USER_DATA !== null) {
      const userInfo = JSON.parse(USER_DATA);
      setUserId(userInfo.id);
    }
    const videoId = extractVideoIdFromSearch(fileUrl);
    if (videoId != null) {
      setUrl(`https://www.youtube.com/watch?v=${videoId}`);
    } else {
      // Process Google Drive URLs to make them playable
      const processedUrl = processVideoUrl(fileUrl);
      setUrl(processedUrl);

      // Log if it's a Google Drive URL for debugging
      if (isGoogleDriveUrl(fileUrl)) {
        console.log('🎬 Original Google Drive URL:', fileUrl);
        console.log('🎯 Processed URL for player:', processedUrl);
      }
    }
    if (topicWise === "true") {
      setBreadcrumbItems(
        getBreadCrumbItems("Video Player Topicwise", {
          section_id: sectionId as string,
          course_id: courseId,
        })
      );
    } else {
      setBreadcrumbItems(
        getBreadCrumbItems("Video Player", {
          course_id: courseId,
        })
      );
    }

    const courses = getLocalStorageItem("courseData");
    if (courses) {
      const parsedData = JSON.parse(courses as string);
      setCourseData(parsedData);
    }
    getLikeData();
    getCommentData();
    checkUserLiked();
    if (exam_result == "") {
      getProgress();
    }
  }, []);

  useEffect(() => {
    window.history.pushState(null, "", window.location.href);
    const handlePopState = () => {
      window.history.pushState(null, "", window.location.href);
    };
    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [router]);

  const getLikeData = () => {
    const fetchLikeData = async (): Promise<void> => {
      const USER_DATA = localStorage.getItem(KEYS.USER_DETAILS);
      let currentUserId = "";
      if (USER_DATA !== null) {
        const userInfo = JSON.parse(USER_DATA);
        currentUserId = userInfo.id;
      }

      const org_id = localStorage.getItem(KEYS.ORG_ID);
      const params = {
        instance_id: instance_id as string,
        org_id: org_id as string,
        activity_type: "like",
      };
      try {
        const result = await getComments(params);

        setIsCommentsLoading(false);
        if (result.length > 0) {
          setLikeCount(result.length);
          setLikeData(result);
          const userHasLiked = result.some(
            (like) =>
              like.user_id === currentUserId && like.activity_type === "like"
          );
          setIsLiked(userHasLiked);
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchLikeData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const getCheckPointsUser = async (): Promise<void> => {
    try {
      const checkPointData = await getCheckPointsdetails(
        course_module_id as string
      );
      setcheckPointDataLength(checkPointData.check_points);
      setcheckPointData(checkPointData.check_points);
      setIsLoading(false);
    } catch (error) {
      throw error;
    }
  };

  const handleProgress = (state: ProgressState) => {
    setPlaybackPosition(state.playedSeconds);
    // if (checkPointsLength.length === 0) {
    const intervalId = setInterval(() => {
      if (!cancelCheckpoint) {
        if (videoEnded) {
          updateProgress("100");
          clearInterval(intervalId);
          console.log("Video ended, progress saving stopped.");
        } else if (hasStarted) {
          saveProgress(); // Call saveProgress every 2 seconds
        }
      } else {
        const [hours, minutes, seconds] = totalLength
          .split(":")
          .map((part: string) => parseInt(part, 10));
        const totalSeconds = hours * 3600 + minutes * 60 + seconds;
        const adjustedPlaybackPosition = Math.max(playbackPosition - 15, 0);
        let percent = (adjustedPlaybackPosition / totalSeconds) * 100;
        updateProgress(percent.toString());
      }
    }, 2000);
    setTimeout(() => {
      clearInterval(intervalId);
    }, 2000);
    // }

    if (state.playedSeconds) {
      const totalSeconds = state.playedSeconds;
      const hours = Math.floor(totalSeconds / 3600); // 3600 seconds in an hour
      const minutes = Math.floor((totalSeconds % 3600) / 60); // Remaining minutes
      const seconds = Math.floor(totalSeconds % 60); // Remaining seconds

      // Create a formatted string for hours, minutes, and seconds
      const formattedTime = `${hours} hour${
        hours !== 1 ? "s" : ""
      } ${minutes} minute${minutes !== 1 ? "s" : ""} ${seconds} second${
        seconds !== 1 ? "s" : ""
      }`;
      setTimeSpent(formattedTime);
      // Log the formatted time
    }

    checkPoints?.forEach((checkpoint) => {
      const targetTime = parseTime(checkpoint.start_time);
      if (Math.abs(state.playedSeconds - targetTime) < 0.5) {
        if (currentCheckpoint !== checkpoint.checkpoint_id) {
          setIsPlaying(false);
          setCheckPoint(checkpoint);
          setCheckpointReached(true);
        }
      }
    });
  };

  const parseTime = (time: string): number => {
    const [hours, minutes, seconds] = time.split(":").map(Number);
    return hours * 3600 + minutes * 60 + seconds;
  };

  const formatDateToString = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based, so add 1
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");
    const milliseconds = String(date.getMilliseconds()).padStart(3, "0");

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  };
  const closeViewer = async (checkpoint: CheckPoint) => {
    try {
      const currentTime = new Date();
      const currentTimeString = formatDateToString(currentTime);
      const org_id = localStorage.getItem(KEYS.ORG_ID);
      const USER_DATA = localStorage.getItem("userDetails");
      if (USER_DATA !== null && USER_DATA !== undefined) {
        const userInfo = JSON.parse(USER_DATA) as LoginUserData;
        const params = {
          checkpoint_id: checkpoint?.checkpoint_id as string,
          user_start_time: currentTimeString,
          org_id: org_id ?? "",
          user_id: userInfo.id,
        };
        const quizData = await startCheckPointQuiz(params);
        localStorage.setItem(KEYS.QUIZ_DATA, JSON.stringify(quizData));
        localStorage.setItem(
          "lastPlaybackPositionForFailed",
          String(playbackPosition - 1)
        );
        localStorage.setItem(
          "lastPlaybackPositionForPassed",
          String(playbackPosition + 1)
        );
        localStorage.removeItem(KEYS.REMAINING_TIME);
        router.push(`/pages/exam-view?checkpoint_exam=${true}&isVideo=${true}`);
        insertLogDetails(
          "Course_Resource",
          "Resource Viewer",
          `Checkpoint exam start`,
          "SUCCESS",
          checkPoint?.checkpoint_id ?? null
        );
      }
    } catch (error) {
      const err = error as ErrorCatch;
      toast({
        variant: "destructive",
        title: ERROR_MESSAGES.error,
        description: err?.message,
      });
      insertLogDetails(
        "Course_Resource",
        "Resource Viewer",
        `Failed to start checkpoint exam `,
        "ERROR",
        checkPoint?.checkpoint_id ?? null
      );
    }
    setCheckpointReached(false);
  };

  const cancelCheckPointModal = () => {
    setCancelCheckpoint(true);
    localStorage.setItem(KEYS.CHECKPOINT_CANCELED, "true");
    setCheckpointReached(false);
    setIsPlaying(true);
    setcheckPointData((prevCheckPoints) =>
      prevCheckPoints.filter(
        (cp) => cp.checkpoint_id !== checkPoint?.checkpoint_id
      )
    );
  };
  const handleReplay = () => {
    setVideoEnded(false); // Reset video end state
    setPlaybackPosition(0); // Reset the playback position
    setIsPlaying(true); // Restart playing the video
    setCheckpointReached(false);
    if (is_checkpoint_enabled) {
      getCheckPointsUser();
    }
  };

  const addFeedback = () => {
    setOpenFeedback(true);
  };

  const closeFeedback = () => {
    setOpenFeedback(false);
    getCommentData();
  };

  const handleComments = (): void => {
    getCommentData();
    setShowComments(!showComments);
  };

  const getCommentData = () => {
    const fetchCommentData = async (): Promise<void> => {
      const org_id = localStorage.getItem(KEYS.ORG_ID);
      const params = {
        instance_id: instance_id as string,
        org_id: org_id as string,
        activity_type: "comment",
      };
      try {
        const result = await getComments(params);

        setIsCommentsLoading(false);
        if (result) {
          setCommentCount(result.length);
          setCommentsData(result);
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchCommentData()
      .then((response) => {
        console.log(response);
      })
      .catch((error) => {
        console.log(error);
      });
  };

  const getProgress = async () => {
    const courseID = getLocalStorageItem(KEYS.COURSE_ID);
    const userID = getLocalStorageItem(KEYS.USER_ID);
    let params: GetCourseProgressRequest = {
      course_id: courseID as string,
      instance_ids: [instance_id],
      user_id: userID as string,
    };
    try {
      const result = await getCourseProgress(params);
      if (result && totalLength) {
        const checkpoint = parsedData.checkpoints ?? [];
        const percentage = result[0].progress;
        if (percentage === 100) {
          setPlaybackPosition(0);
        } else {
          const [hours, minutes, seconds] = totalLength
            .split(":")
            .map((part: string) => parseInt(part, 10));
          const totalSeconds = hours * 3600 + minutes * 60 + seconds;
          const progressInSeconds = (percentage / 100) * totalSeconds;

          if (checkPoint === null) {
            setPlaybackPosition(progressInSeconds);
          }
          if (checkpoint.length === 1) {
            // Single checkpoint logic
            const checkpointStatus = checkpoint[0].status;
            if (checkpointStatus === "FAILED") {
              setPlaybackPosition(0); // Start from the beginning
            } else if (
              checkpointStatus === null ||
              checkpointStatus === "PENDING"
            ) {
              setPlaybackPosition(progressInSeconds - 10);
            } else {
              setPlaybackPosition(progressInSeconds + 1); // Start from progress + 1 sec
            }
          }
          if (checkpoint.length > 1) {
            const allPassed = checkpoint.every(
              (cp: { status: string }) => cp.status === "PASSED"
            );

            if (allPassed) {
              // If all checkpoints are passed, continue from current progress
              setPlaybackPosition(progressInSeconds + 1);
              return;
            }
            // First sort checkpoints by their start_time
            const sortedCheckpoints = [...checkpoint].sort(
              (a, b) =>
                timeToSeconds(a.start_time) - timeToSeconds(b.start_time)
            );

            // Find the first failed checkpoint in current attempt (if any)
            const firstFailedCheckpoint = sortedCheckpoints.find(
              (cp) => cp.status === "FAILED"
            );

            if (firstFailedCheckpoint) {
              // Find the last passed checkpoint that occurs BEFORE the first failure
              const lastPassedBeforeFailure = sortedCheckpoints
                .filter(
                  (cp) =>
                    cp.status === "PASSED" &&
                    timeToSeconds(cp.start_time) <
                      timeToSeconds(firstFailedCheckpoint.start_time)
                )
                .pop();

              if (lastPassedBeforeFailure) {
                // If there was a passed checkpoint before the failure, start from there
                setPlaybackPosition(
                  timeToSeconds(lastPassedBeforeFailure.start_time) + 1
                );
              } else {
                // If we failed the first checkpoint or no passes before failure, start from beginning
                setPlaybackPosition(0);
              }
            } else {
              // If no failures in current attempt, continue normally
              setPlaybackPosition(progressInSeconds + 1);
            }
          }
        }
        const internalPlayer = playerRef.current;
        if (internalPlayer?.seekTo) {
          internalPlayer?.seekTo(playbackPosition, "seconds");
        }
      }
    } catch (error) {
      console.log(error);
    }
  };
  const updateCheckpointProgress = async () => {
    const userID = getLocalStorageItem(KEYS.USER_ID);
    let params: GetSessionReport = {
      course_id: courseId as string,
      course_module_id: course_module_id,
      user_id: userID,
    };
    try {
      const result = await getSessionReport(params);

      if (result) {
        const allCheckpointsPassed =
          result?.session_data[0]?.modules[0]?.checkpoints.every(
            (checkpoint) => {
              const lastSession =
                checkpoint.sessions[checkpoint.sessions.length - 1];
              return lastSession.result === "PASSED";
            }
          );
        const isCheckpointCanceled =
          localStorage.getItem(KEYS.CHECKPOINT_CANCELED) === "true";
        if (allCheckpointsPassed === true && !isCheckpointCanceled) {
          updateProgress("100");
        }
      }
    } catch (error) {
      console.log(error);
    }
  };
  const checkUserLiked = () => {
    const liked = LikeData.some(
      (comment) =>
        comment.user_id === userId && comment.activity_type === "like"
    );

    setIsLiked(liked);
  };
  const updateProgress = async (percent: string) => {
    const orgID = getLocalStorageItem(KEYS.ORG_ID);
    const userID = getLocalStorageItem(KEYS.USER_ID);
    let params: CourseProgressRequestType = {
      course_id: courseId as string,
      instance_id: instance_id,
      org_id: orgID as string,
      progress_data: { progress: percent, time_spent: timeSpent },
      user_id: userID as string,
    };
    try {
      const result = await updateCourseProgress(params);
      if (result) {
        localStorage.setItem(KEYS.CHECKPOINT_CANCELED, "false");
        if (btnClick) {
          let msg =
            percent === "100" ? t("progress_complete_msg") : t("progress_msg");

          toast({
            variant: "success",

            title: t("success"),

            description: msg,
          });
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  const saveProgress = () => {
    const [hours, minutes, seconds] = totalLength
      .split(":")
      .map((part: string) => parseInt(part, 10));
    const totalSeconds = hours * 3600 + minutes * 60 + seconds;
    let percent = (playbackPosition / totalSeconds) * 100;
    updateProgress(percent.toString());
  };

  const handleSaveProgress = () => {
    setBtnClick(true); // Set the button state to true
    saveProgress(); // Call the saveProgress function
  };
  const handleReady = () => {
    if (hasHandledOnReady.current) return;
    hasHandledOnReady.current = true;

    const internalPlayer = playerRef.current;

    if (exam_result === "PASSED") {
      setIsPlaying(true);
      const lastPlaybackPositionForPassed = localStorage.getItem(
        "lastPlaybackPositionForPassed"
      );
      if (lastPlaybackPositionForPassed) {
        internalPlayer?.seekTo(
          parseFloat(lastPlaybackPositionForPassed),
          "seconds"
        );
      } else {
        internalPlayer?.seekTo(playbackPosition, "seconds");
      }
    } else if (exam_result === "FAILED") {
      setIsPlaying(true);
      const lastPlaybackPositionForFailed = localStorage.getItem(
        "lastPlaybackPositionForFailed"
      );
      if (lastPlaybackPositionForFailed) {
        internalPlayer?.seekTo(
          parseFloat(lastPlaybackPositionForFailed) - 0.5,
          "seconds"
        );
      } else {
        internalPlayer?.seekTo(playbackPosition, "seconds");
      }
    } else {
      internalPlayer?.seekTo(playbackPosition, "seconds");
    }
  };

  const handleLike = async () => {
    const reqParams = {
      comment_data: {
        subject: "",
        message: "",
        type: "Feedback",
        parent_id: null,
        activity_type: "like",
      },
      instance_id: instance_id,
      user_id: userId,
    };

    try {
      const result = await addComments(reqParams);

      if (result.status === "success") {
        toast({
          variant: "default",
          title: isLiked ? t("unliked_title") : t("liked_title"),
          description: isLiked
            ? t("unliked_description")
            : t("liked_description"),
        });
        setIsLiked(!isLiked);
        setLikeCount((prevCount) => (isLiked ? prevCount - 1 : prevCount + 1));
      }

      await insertLogDetails(
        "Course_Resource",
        "Resource Viewer",
        `${fileName} Video Liked  `,
        "SUCCESS",
        result.comment_id
      );
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Action failed.",
      });
      await insertLogDetails(
        "Course_Resource",
        "Resource Viewer",
        `Failed to like ${fileName} `,
        "ERROR",
        UUID
      );
    }
  };
  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      {!isLoading ? (
        <Card className="w-full sm:w-3/4 lg:w-1/2 mx-auto p-4 sm:p-6 text-[var(--color-font-color)]">
          <div className="flex flex-col">
            <div className="">
              <div
                role="tabpanel"
                id="panel-video-view"
                aria-labelledby="video-view"
                hidden={activeTab !== "video-view"}
                className="focus:outline-none"
              >
                <div className="flex justify-end pb-2  -mb-4 "></div>

                <div className="w-full  mb-2">
                  <p className="text-lg font-semibold mt-2 mb-2">
                    {t("Topic")} : {topicName}
                  </p>
                  <p className="text-lg font-semibold mt-2 mb-2">
                    {t("Name")} : {videoName}
                  </p>
                </div>
                <div className="space-y-4 bg-gray-50 rounded-lg">
                  <div className="w-full mb-4 lg:mb-0">
                    <div>
                      {isGoogleDriveUrl(fileUrl) ? (
                        // Render iframe for Google Drive URLs
                        <iframe
                          src={getGoogleDriveIframeUrl(fileUrl)}
                          width="100%"
                          height={window.innerWidth >= 1024 ? "500px" : "200px"}
                         allow="autoplay; encrypted-media"
                          allowFullScreen
                          title="Google Drive Video"
                          className="rounded-lg"
                           onPlay={() => {
                          setHasStarted(true);
                          setIsPlaying(true);
                           }}
                        />
                      ) : (
                        // Render ReactPlayer for other URLs
                        <ReactPlayer
                          ref={playerRef}
                          className="custom-player"
                          light={thumbnailImage}
                          width="100%"
                          height={window.innerWidth >= 1024 ? "500px" : "200px"}
                          url={url}
                          controls={checkPointsLength.length !== 0 ? false : true}
                          playing={isPlaying}
                          onProgress={handleProgress}
                          onReady={handleReady}
                          onPlay={() => {
                          setHasStarted(true);
                          setIsPlaying(true);
                          setDisableProgress(false);
                          if (!isLogged) {
                            const [hours, minutes, seconds] = totalLength
                              .split(":")
                              .map((part: string) => parseInt(part, 10));
                            const totalSeconds =
                              hours * 3600 + minutes * 60 + seconds;
                            const percent = totalSeconds
                              ? (
                                  (playbackPosition / totalSeconds) *
                                  100
                                ).toFixed(2)
                              : "0";
                            insertLogDetails(
                              "Course_Resource",
                              "Video Start",
                              `Video started with progress: ${percent}%`,
                              "SUCCESS",
                              instance_id
                            );
                            setIsLogged(true);
                          }
                        }}
                        onEnded={() => {
                          setIsPlaying(false);
                          if (checkPointsLength.length === 0) {
                            updateProgress("100");
                          } else {
                            updateCheckpointProgress();
                          }
                          setVideoEnded(true);
                          insertLogDetails(
                            "Course_Resource",
                            "Resource Viewer",
                            `${fileName} Video completed  `,
                            "SUCCESS",
                            instance_id
                          );
                        }}
                        />
                      )}
                    </div>
                    <div className="flex items-center justify-center gap-4 mt-4">
                      {hasStarted && checkPointsLength.length === 0 && (
                        <>
                          {/* Rewind */}
                          <div className="flex items-center gap-2">
                            <select
                              className="text-sm border border-gray-300 rounded px-2 py-1"
                              value={skipBack}
                              onChange={(e) =>
                                setSkipBack(parseInt(e.target.value))
                              }
                            >
                              <option value="10">10s</option>
                              <option value="20">20s</option>
                              <option value="30">30s</option>
                            </select>
                            <button
                              onClick={() => {
                                const internalPlayer = playerRef.current;
                                if (internalPlayer) {
                                  const currentTime =
                                    playbackPosition - skipBack;
                                  internalPlayer.seekTo(
                                    Math.max(currentTime, 0),
                                    "seconds"
                                  );
                                  setPlaybackPosition(Math.max(currentTime, 0));
                                }
                              }}
                              className="p-3 bg-gray-500 text-white rounded-full flex items-center justify-center"
                            >
                              -{skipBack}s
                            </button>
                          </div>

                          {/* Play/Pause */}
                          <button
                            onClick={() => setIsPlaying(!isPlaying)}
                            className="p-3 bg-blue-500 text-white rounded-full flex items-center justify-center"
                          >
                            {isPlaying ? (
                              <Pause size={20} />
                            ) : (
                              <Play size={20} />
                            )}
                          </button>

                          {/* Forward */}
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => {
                                const internalPlayer = playerRef.current;
                                if (internalPlayer) {
                                  const currentTime =
                                    playbackPosition + skipForward;
                                  internalPlayer.seekTo(currentTime, "seconds");
                                  setPlaybackPosition(currentTime);
                                }
                              }}
                              className="p-3 bg-gray-500 text-white rounded-full flex items-center justify-center"
                            >
                              +{skipForward}s
                            </button>
                            <select
                              className="text-sm border border-gray-300 rounded px-2 py-1"
                              value={skipForward}
                              onChange={(e) =>
                                setSkipForward(parseInt(e.target.value))
                              }
                            >
                              <option value="10">10s</option>
                              <option value="20">20s</option>
                              <option value="30">30s</option>
                            </select>
                          </div>
                        </>
                      )}
                    </div>
                    <p className="text-lg">
                      {" "}
                      {t("Video Length")} : {length}
                    </p>
                    <p className="text-lg">
                      {t("Description")} : {parsedData.description}
                    </p>
                  </div>
                </div>

                {videoEnded && checkPointsLength.length === 0 && (
                  <div className="mt-4 flex justify-end">
                    <Button onClick={handleReplay} variant="default">
                      {t("Replay Video")}
                    </Button>
                  </div>
                )}
                <div className="flex justify-between items-center mt-4 mb-4">
                  <div className="flex items-center gap-2 ">
                    <button
                      onClick={handleLike}
                      className="flex items-center gap-2 text-black-600 bg-gray-100 p-2 rounded transition"
                      title={isLiked ? "Unlike" : "Like"}
                    >
                      <ThumbsUp
                        className={`w-5 h-5 ${
                          isLiked
                            ? "text-blue-600 fill-blue-600"
                            : "text-gray-600"
                        }`}
                        fill={isLiked ? "currentColor" : "none"}
                      />

                      {likeCount > 0 && (
                        <span className="text-sm font-medium text-[var(--color-font-color)]">
                          {likeCount} {t(likeCount === 1 ? "Like" : "Likes")}
                        </span>
                      )}
                    </button>
                  </div>

                  <button
                    onClick={handleComments}
                    className="flex items-center gap-2 text-black-600 bg-gray-100 p-2 rounded transition"
                  >
                    <MessageCircle className="w-5 h-5 text-orange-600" />
                    <span className="text-sm font-medium text-[var(--color-font-color)]">
                      {t("Comments")}
                    </span>
                  </button>
                </div>
                {showComments && (
                  <div>
                    <div className="flex justify-between items-center gap-2 mb-2 mt-5">
                      <div className="flex items-center gap-2">
                        <MessageCircle className="w-5 h-5 text-orange-600" />
                        <h2 className="text-xl font-semibold">
                          {" "}
                          {t("Comments")}
                        </h2>
                      </div>
                      <div className="cursor-pointer" title="Add comments">
                        <MessageSquarePlus
                          color="orange"
                          size={24}
                          onClick={addFeedback}
                        />
                      </div>
                    </div>

                    {!isCommentsLoding ? (
                      <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                        <CommentsSection
                          id={instance_id}
                          commentsData={commentsData}
                          onCommentAdded={getCommentData}
                        ></CommentsSection>
                      </div>
                    ) : (
                      <Spinner></Spinner>
                    )}
                  </div>
                )}
                {checkpointReached && (
                  <Modal
                    title=""
                    header=""
                    openDialog={checkpointReached}
                    closeDialog={() => {
                      checkPoint ? closeViewer(checkPoint) : "";
                    }}
                    type="max-w-3xl"
                  >
                    <CheckPointModal
                      closeDialog={() => {
                        checkPoint ? closeViewer(checkPoint) : "";
                      }}
                      cancelCheckPoint={() => {
                        cancelCheckPointModal();
                      }}
                    />
                  </Modal>
                )}
                {openFeedback && (
                  <Modal
                    // title="Add Comments"
                    title={t("Add Comments")}
                    header=""
                    openDialog={openFeedback}
                    closeDialog={() => {
                      closeFeedback();
                    }}
                    type="max-w-xl"
                  >
                    <CommentsModal
                      closeDialog={() => {
                        closeFeedback();
                      }}
                      instanceId={instance_id}
                    />
                  </Modal>
                )}
                {/* {openFeedback && (
                <Modal
                  title="Add Comments"
                  header=""
                  openDialog={openFeedback}
                  closeDialog={() => {
                    closeFeedback();
                  }}
                  type="max-w-xl"
                >
                  <CommentsModal
                    closeDialog={() => {
                      closeFeedback();
                    }}
                    instanceId={instance_id}
                  />
                </Modal>
              )} */}
              </div>

              <div
                role="tabpanel"
                id="panel-comments"
                aria-labelledby="comments"
                hidden={activeTab !== "comments"}
                className="focus:outline-none"
              >
                <div className="flex items-center gap-2 mb-4">
                  <MessageSquareCodeIcon className="w-5 h-5 text-pink-600" />
                  <h2 className="text-xl font-semibold"> {t("Comments")}</h2>
                </div>
                <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                  <CommentsSection
                    id={instance_id}
                    commentsData={commentsData}
                  ></CommentsSection>
                </div>
              </div>
            </div>
          </div>
        </Card>
      ) : (
        <Spinner></Spinner>
      )}
      <div className="flex justify-end mt-4 sticky bottom-2 px-2">
        <Button
          variant="outline"
          className="w-full sm:w-auto text-white bg-black hover:bg-neutral-900 rounded-md"
          onClick={() => {
            if (topicWise === "true") {
              // Check if we came from subject-details page
              const resourceData = localStorage.getItem(KEYS.RESOURCE_DATA);
              if (resourceData) {
                const parsedData = JSON.parse(resourceData);
                // If the resource data has topic_name, it likely came from subject-details
                router.push(
                  `/pages/course-resource?section_id=${sectionId}&course_id=${courseId}`
                );
                // if (parsedData.topic_name) {
                //   router.push(
                //     `/pages/subject-details?section_id=${sectionId}&course_id=${courseId}`
                //   );
                // } else {
                //   router.push(
                //     `/pages/course-resource?section_id=${sectionId}&course_id=${courseId}`
                //   );
                // }
              } else {
                router.push(
                  `/pages/course-resource?section_id=${sectionId}&course_id=${courseId}`
                );
              }
            } else {
              if (process.env.NEXT_PUBLIC_SUBJECT_WISE_LIST === "true") {
                router.push(`/pages/section-details`);
              } else {
                router.push(`/pages/course-details?course_id=${courseId}`);
              }
            }
          }}
        >
          {t("Back")}
        </Button>
      </div>
      {/* <div className="border p-4">
        <h2 className="text-xl font-semibold mb-4">{fileName}</h2>
        <div className="w-full flex flex-col lg:flex-row md:pr-8 p-4">
          <div className="w-full lg:w-1/2 mb-4 lg:mb-0 lg:pr-4">
            <div className="rounded-md overflow-hidden">
              <ReactPlayer
                className=""
                light={true}
                width="100%"
                height="500px"
                url={fileUrl}
                controls={checkPoints.length>0?false:true}
                // controls={true}
                playing={isPlaying}
                onProgress={handleProgress}
                onReady={(player) => {
                  if (exam_result === "PASSED") {
                    setIsPlaying(true);
                    const lastPlaybackPositionForPassed = localStorage.getItem(
                      "lastPlaybackPositionForPassed"
                    );
                    if (lastPlaybackPositionForPassed) {
                      player.seekTo(
                        parseFloat(lastPlaybackPositionForPassed),
                        "seconds"
                      );
                    } else {
                      player.seekTo(playbackPosition, "seconds");
                    }
                  } else if (exam_result === "FAILED") {
                    setIsPlaying(true);
                    const lastPlaybackPositionForFailed = localStorage.getItem(
                      "lastPlaybackPositionForFailed"
                    );
                    if (lastPlaybackPositionForFailed) {
                      player.seekTo(
                        parseFloat(lastPlaybackPositionForFailed) - 0.5,
                        "seconds"
                      );
                    } else {
                      player.seekTo(playbackPosition, "seconds");
                    }
                  } else {
                    player.seekTo(playbackPosition, "seconds");
                  }
                }}
                onPlay={() => {
                  setIsPlaying(true);
                }}
              />
            </div>
            <p className="text-lg mt-2">{parsedData.description}</p>
            { checkPoints.length>0  && ( <p className="text-lg text-red-500">{ERROR_MESSAGES.checkpint_msg}</p>)}
          </div>

          <div className="w-full lg:w-1/2 p-2 rounded-md lg:m-4 lg:m-0 h-full">
            <CommentsSection id={instance_id}></CommentsSection>
          </div>
        </div>
      </div>
      <div className="flex justify-end mt-4">
        <Button
          variant="outline"
          className="w-full sm:w-auto"
          onClick={() => {
            setIsPlaying(false);
            if (sectionId) {
              // Check if we came from subject-details page
              const resourceData = localStorage.getItem(KEYS.RESOURCE_DATA);
              if (resourceData) {
                const parsedData = JSON.parse(resourceData);
                // If the resource data has topic_name, it likely came from subject-details
                if (parsedData.topic_name) {
                  router.push(`/pages/subject-details?section_id=${sectionId}&course_id=${courseId}`);
                } else {
                  router.push(`/pages/course-resource?section_id=${sectionId}`);
                }
              } else {
                router.push(`/pages/course-resource?section_id=${sectionId}`);
              }
            } else {
              router.push(`/pages/dashboard`);
            }
          }}
        >
          Back
        </Button>
      </div>
      {checkpointReached && (
        <Modal
          title=""
          header=""
          openDialog={checkpointReached}
          closeDialog={() => {
            checkPoint ? closeViewer(checkPoint) : "";
          }}
          type="max-w-3xl"
        >
          <CheckPointModal
            closeDialog={() => {
              checkPoint ? closeViewer(checkPoint) : "";
            }}
            cancelCheckPoint = {()=>{
              cancelCheckPointModal()
            }}
          />
        </Modal>
      )} */}
    </MainLayout>
  );
};

export default VideoPlayer;
