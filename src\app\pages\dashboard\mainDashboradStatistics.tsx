"use client";
import React, { useEffect, useState } from "react";
import { getLocalStorageItem } from "@/lib/utils";
import { useCourse } from "@/hooks/useCourse";
import { KEYS } from "@/lib/keys";
import { CourseStats, SummaryStatistics, UserStatistics } from "@/types";
import { Spinner } from "@/components/ui/progressiveLoder";
import { Modal } from "@/components/ui/modal";
import CourseDetailsModal from "./courseDetailsModal";
import { useTranslation } from "next-i18next";
import {
  ACHIEVEMENTS,
  COMPLETED_COURSE,
  COVERED_PERCENTAGE,
  COVERED_TIME,
  TOTAL_COURSE,
} from "@/lib/constants";
import { Card, CardContent } from "@/components/ui/card";
import { convertTimeStringToHours } from "@/lib/timeUtils";
import "../../../styles/dashboard.css";
import Image from "next/image";
interface CourseStatistics {
  totalMarksGot: number;
  percCompleted: number;
  hoursCompleted: string;
  achievements: number;
}

interface CourseStatisticsProps {
  courseId: string;
}

const OverallStatisticsCounts = ({ courseId }: CourseStatisticsProps) => {
  const { t } = useTranslation("common");
  const { getUserAllCourseStatics } = useCourse();
  const orgID = getLocalStorageItem(KEYS.ORG_ID);
  const [isLoading, setIsLoading] = useState(false);
  const [courseStatisticsAll, setCourseStaticsAll] = React.useState<
    UserStatistics[]
  >([]);
  const [userStatistics, setUserStatics] = React.useState<UserStatistics[]>([]);
  const [dashboardStats, setDashboardStats] =
    React.useState<CourseStatistics>();
  const [courseListWithCounter, setCourseListWithCounter] = React.useState<
    CourseStats[]
  >([]);
  const [openModal, setIsOpenModal] = React.useState<boolean>(false);
  const [courseLength, setCourseLength] = useState<number>(0);
  const [summaryData, setSummaryData] = useState<SummaryStatistics[]>([]);
  const [modalTitle, setModalTitle] = useState<string>("");

  useEffect(() => {
    const courses = getLocalStorageItem("courseData");
    const parsedData = JSON.parse(courses as string);
    // setCourseLength(parsedData.length);
    getAllCourseStatistics();
  }, []);

  // Function to convert HH:MM:SS to seconds
  const timeToSeconds = (time: string) => {
    const [hours, minutes, seconds] = time.split(":").map(Number);
    return hours * 3600 + minutes * 60 + seconds;
  };

  const getAllCourseStatistics = async (): Promise<void> => {
    try {
      let user_id = getLocalStorageItem(KEYS.USER_ID) || "";
      let apiParams = {
        org_id: orgID as string,
        user_id: user_id as string,
      };
      const response = await getUserAllCourseStatics(apiParams);
      console.log("response", response);

      setCourseStaticsAll(response);
      setIsLoading(true);
      setCourseLength(response?.length);
      const firstFiveItems = response.slice(0, 5);
      const allItems = response;
      setUserStatics(firstFiveItems);

      // calculate cumulative percentage of time
      const totalProgress = allItems.reduce(
        (sum, progress) => sum + progress.progress,
        0
      );
      const cumulativePercentage = totalProgress / allItems.length;

      // Calculate the total time spent
      const totalTimeInSeconds = allItems.reduce((sum, progress) => {
        if (progress.time_spent !== null) {
          return sum + timeToSeconds(String(progress.time_spent));
        }
        return sum;
      }, 0);
      const completedCount = allItems.filter(
        (item) => item.progress >= 100
      ).length;

      // Convert back to HH:MM:SS format
      const totalHoursCovered = totalTimeInSeconds;

      const totalMarks =
        allItems.reduce(
          (accumulator, currentValue) =>
            accumulator + Number(currentValue.totalMarks),
          0
        ) || 0;
      const totalPercentCovered =
        allItems.reduce(
          (accumulator, currentValue) => cumulativePercentage,
          0
        ) || 0;

      const totalAchievements =
        allItems.reduce(
          (accumulator, currentValue) =>
            accumulator + Number(currentValue.achievements),
          0
        ) || 0;
      const totalCourse = allItems?.length;
      const overAllStatus = {
        totalMarksGot: totalMarks,
        percCompleted: totalPercentCovered,
        hoursCompleted: String(totalHoursCovered),
        achievements: totalAchievements,
        completedCount: completedCount,
        totalCourse: totalCourse,
      };

      setDashboardStats(overAllStatus as CourseStatistics);
      const studyHours = convertTimeStringToHours(overAllStatus.hoursCompleted);
      const courses = getLocalStorageItem("courseData");
      const parsedData = JSON.parse(courses as string);
      setSummaryData([
        {
          title: TOTAL_COURSE,
          value: overAllStatus.totalCourse,
          trend: "",
          gradient: "bg-total-course",
          icon: (
            <Image
              src="/assets/total-course.png"
              alt="Profile Image"
              width={50}
              height={50}
              className=""
            />
          ),
          textColor: "text-white",
        },
        {
          title: COMPLETED_COURSE,
          value: overAllStatus.completedCount,
          trend: "",
          gradient: "bg-total-marks",
          // "bg-[linear-gradient(90deg,_rgba(228,74,138,1)_0%,_rgba(212,77,146,1)_51%,_rgba(198,80,158,1)_100%)]",
          icon: (
            <Image
              src="/assets/total-marks.png"
              alt="Profile Image"
              width={50}
              height={50}
              className=""
            />
          ),
          // <BarChart className="w-10 h-10" />,
          textColor: "text-white",
        },
        {
          title: COVERED_PERCENTAGE,
          value:
            overAllStatus.percCompleted > 100
              ? "100%"
              : `${overAllStatus.percCompleted.toFixed(2)}%`,
          trend: "",
          gradient: "bg-progress",
          // "bg-[linear-gradient(90deg,_rgba(134,79,225,1)_0%,_rgba(109,68,196,1)_51%,_rgba(84,56,166,1)_100%)]",
          icon: (
            <Image
              src="/assets/progress.png"
              alt="Profile Image"
              width={50}
              height={50}
              className=""
            />
          ),
          // icon: <CheckCircle className="w-10 h-10" />,
          textColor: "text-white",
        },
        {
          title: COVERED_TIME,
          value: `${studyHours}`,
          trend: "",
          gradient: "bg-time-spent",
          // "bg-[linear-gradient(90deg,_rgba(77,185,234,1)_0%,_rgba(84,173,231,1)_51%,_rgba(92,162,224,1)_100%)]",
          icon: (
            <Image
              src="/assets/time-spent.png"
              alt="Profile Image"
              width={50}
              height={50}
              className=""
            />
          ),
          // icon: <Clock className="w-10 h-10" />,
          textColor: "text-white",
        },
        {
          title: ACHIEVEMENTS,
          value: `${overAllStatus.achievements}`,
          trend: "",
          gradient: "bg-achievements",

          icon: (
            <Image
              src="/assets/acheivment.png"
              alt="Profile Image"
              width={50}
              height={50}
              className=""
            />
          ),
          // icon: <Award className="w-10 h-10" />,
          textColor: "text-white",
        },
      ]);
    } catch (error) {
      setIsLoading(true);
    }
  };
  const showCardContents = (detailsType: string) => {
    switch (detailsType) {
      case TOTAL_COURSE:
        const statsData = courseStatisticsAll.map((stats) => ({
          course_name: stats.course_name,
          valuetype: "",
          course_id: stats.course_id,
        }));
        setCourseListWithCounter(statsData as CourseStats[]);
        break;
      case COMPLETED_COURSE:
        const completedData = courseStatisticsAll
          .filter((stats) => stats.progress === 100) // Filter courses with 100% progress
          .map((stats) => ({
            course_name: stats.course_name,
            valuetype: "Progress",
            value: stats.progress,
            course_id: stats.course_id,
          }));
        setCourseListWithCounter(completedData as CourseStats[]);
        break;
      case COVERED_PERCENTAGE:
        const coveredPerncData = courseStatisticsAll.map((stats) => ({
          course_name: stats.course_name,

          valuetype: t("Progress"),
          value: stats.progress,
          course_id: stats.course_id,
        }));
        setCourseListWithCounter(coveredPerncData as CourseStats[]);
        break;
      case COVERED_TIME:
        const coveredTimeData = courseStatisticsAll.map((stats) => ({
          course_name: stats.course_name,
          valuetype: t("Time Spent"),
          value: stats.time_spent ?? 0,
          course_id: stats.course_id,
        }));
        setCourseListWithCounter(coveredTimeData as CourseStats[]);
        break;
      case ACHIEVEMENTS:
        const achievementsData = courseStatisticsAll.map((stats) => ({
          course_name: stats.course_name,
          // valuetype: "Achievements",
          valuetype: t("Achievements"),
          value: stats.achievements,
          course_id: stats.course_id,
        }));
        setCourseListWithCounter(achievementsData as CourseStats[]);
        break;
    }
    setModalTitle(detailsType);
    setIsOpenModal(true);
  };
  return (
    <div>
      <h2 className="text-2xl font-bold pb-0 pb-2"> {t("Course Overview")}</h2>
      {isLoading ? (
        <main className="w-full">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
            {summaryData.map((item, index) => (
              <Card
                key={index}
                // onClick={() => showCardContents(item.title)}
                onClick={() => showCardContents(item.title)}
                className={`transition-all transform rounded duration-500 w-100 cursor-pointer
                         
                          ${item.gradient}
                      backdrop-blur-xl bg-opacity-5
                         border border-white/20 shadow-xl
                         `}
              >
                <CardContent>
                  <div
                    className={`mt-2 text-base ${
                      item.textColor || "text-black"
                    }`}
                  >
                    {/* {item.title} */}
                    {t(item.title)}
                  </div>
                  <div
                    className={`text-2xl font-bold ${
                      item.textColor || "text-white"
                    } flex justify-center`}
                  >
                    {item.value}
                  </div>

                  <div
                    className={`bottom-5 left-3 ${
                      item.textColor || "text-white"
                    }  flex items-center`}
                  >
                    {item.icon}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </main>
      ) : (
        <Spinner></Spinner>
      )}

      {openModal && (
        <Modal
          title=""
          header=""
          openDialog={openModal}
          closeDialog={() => {
            setIsOpenModal(false);
          }}
          type="max-w-3xl"
        >
          <CourseDetailsModal
            closeDialog={() => {
              setIsOpenModal(false);
            }}
            CourseStatistics={courseListWithCounter}
            CourseId={courseId}
            isMain={true}
            title={modalTitle}
          />
        </Modal>
      )}
    </div>
  );
};

export default OverallStatisticsCounts;
