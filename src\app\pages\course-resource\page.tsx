"use client";
import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { FilePlus2, Folders, Eye, FileSearch, IterationCw } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Stepper } from "primereact/stepper";
import { StepperPanel } from "primereact/stepperpanel";
import "primereact/resources/primereact.css";
import "primereact/resources/themes/lara-light-indigo/theme.css";
import "../../../styles/stepper.css";
import { Separator } from "@/components/ui/separator";
import MainLayout from "../layouts/mainLayout";
import { useTranslation } from "next-i18next";
import {
  FolderResourceType,
  FolderType,
  InnerItem,
  ResoureceModuleType,
  SectionViewResultType,
  SkipVideoRequest,
  ToastType,
  ViewResourcePageType,
} from "@/types";
import { useSearchParams } from "next/navigation";
import { useCourse } from "@/hooks/useCourse";
import "../../../styles/main.css";
import { useRouter } from "next/navigation";
import { KEYS } from "@/lib/keys";
import { getFileType } from "@/lib/constants";
import VideoDialog from "@/components/ui/video-dialog";
import { Modal } from "@/components/ui/modal";
import DocumentDialog from "@/components/pptDialog";
import { Button } from "@/components/ui/button";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";
import { useToast } from "@/components/ui/use-toast";
import SkipModal from "@/components/skip-video-dialog";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";

export default function CourseResource(): React.JSX.Element {
  const { t } = useTranslation("common");
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("resources");
  const [openFolders, setOpenFolders] = useState<boolean>(false);
  const stepperRef = useRef(null);
  const searchParams = useSearchParams();
  const sectionId = searchParams?.get("section_id");
  const courseId = searchParams?.get("course_id");
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setOpenFolders(false);
  };
  const [viewOpen, setViewOpen] = React.useState(false);
  const [viewOpenPPT, setViewOpenPPT] = React.useState(false);
  const [moduleData, setModuleData] = useState<ResoureceModuleType[]>([]);
  const [folderData, setFolderData] = useState<FolderType[]>([]);
  const [folderResource, setFolderResource] = useState<FolderType>();
  const { sectionDetails, viewResourcePage, skipResource } = useCourse();
  const [type, setType] = useState<string>("");
  const [selectedItem, setSelectedItem] = useState<ResoureceModuleType>();
  const [openSkipModal, setOpenSkipModal] = useState<boolean>(false);
  const [pageCount, setPageCount] = React.useState<number>(0);
  const [url, setUrl] = useState("");
  const { toast } = useToast() as unknown as ToastType;
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  const handleClickModule = (data: ResoureceModuleType) => {
    openFileViewer(data);
  };
  const handleClickFolder = (data: FolderResourceType) => {
    openFileViewer(data);
  };
  const isImageOrPdfUrl = (url: string, item: ResoureceModuleType): string => {
    if (item.extension === "pdf") {
      return "pdf";
    } else if (
      item.extension === "jpg" ||
      item.extension === "jpeg" ||
      item.extension === "png" ||
      item.extension === "gif" ||
      item.extension === "bmp"
    ) {
      return "image";
    } else if (
      item.extension === "ppt" ||
      item.extension === "pptx" ||
      item.extension === "doc" ||
      item.extension === "docx" ||
      item.extension === "xls" ||
      item.extension === "xlsx"
    ) {
      return "document";
    } else if (item.extension === "mp4") {
      return "video";
    } else {
      return "image";
    }
  };
  const openFileViewer = async (data: ResoureceModuleType) => {
    const response = await viewResourcePage(
      data.module_type,
      data.instance,
      data.course_module_id
    );
    const resultData = response as ViewResourcePageType;
    const totalPages = resultData.page_count;
    localStorage.setItem(KEYS.RESOURCE_DATA, JSON.stringify(resultData));
    
    if (resultData?.module_source === "File" || resultData?.module_source === "Document") {
      const fileTypeCheck = isImageOrPdfUrl(resultData?.url, data);
      if (fileTypeCheck === "image")
        router.push(
          `/pages/image-viewer?section_id=${sectionId}&is_topic_wise=${true}&course_id=${courseId}`
        );
      if (fileTypeCheck === "pdf")
        router.push(
          `/pages/pdf-viewer?section_id=${sectionId}&page_count=${totalPages}&course_id=${courseId}&is_topic_wise=${true}`
        );
      if (fileTypeCheck === "document") {
        if (data.progress < 100) {
          router.push(
            `/pages/document-viewer?section_id=${sectionId}&page_count=${totalPages}&course_id=${courseId}&progress=${
              data?.progress
            }&is_topic_wise=${true}`
          );
        } else {
          setPageCount(data.page_count as number);
          setViewOpenPPT(true);
          setUrl(data.external_url);
        }
      }
      // router.push(
      //   `/pages/document-viewer?section_id=${sectionId}&page_count=${totalPages}`
      // );
    } else if (resultData?.module_source === "Video") {
      if (data.progress < 100) {
        router.push(
          `/pages/video-player?course_id=${courseId}&is_topic_wise=${true}&section_id=${sectionId}`
        );
      } else {
        setViewOpen(true);
        setUrl(data.external_url);
      }
    } else {
      router.push(
        `/pages/html-viewer?course_id=${data?.course_id}&progress=${
          data?.progress
        }&is_topic_wise=${true}&section_id=${sectionId}`
      );
    }
  };

  useEffect(() => {
    setBreadcrumbItems(
      getBreadCrumbItems("Section Details", {
        course_id: courseId as string,
      })
    );
    localStorage.removeItem(KEYS.VISITED_CHECKPOINTS);
    localStorage.removeItem(KEYS.CURRENT_SLIDE);
    fetchData();
  }, []);
  const fetchData = async (): Promise<void> => {
    try {
      const response = await sectionDetails(
        sectionId as string,
        courseId as string
      );

      response.modules = response.modules?.filter(
        (module) => module.module_type !== "Quiz"
      );
      const result = response as SectionViewResultType;
      setModuleData(result.modules);
      setFolderData(result.folders);
    } catch (error) {}
  };
  function openFolder(folder: FolderType): void {
    setOpenFolders(true);
    setFolderResource(folder);
  }
  const closeDialog = (): void => {
    setViewOpen(false);
    setViewOpenPPT(false);
    setOpenSkipModal(false);
  };
  const skipModuleResources = async (item: ResoureceModuleType) => {
    const orgID = localStorage.getItem(KEYS.ORG_ID);
    const userId = localStorage.getItem("userId");
    let reqParams: SkipVideoRequest = {
      org_id: orgID as string,
      course_id: courseId as string,
      resource_id: item.instance as string,
      user_id: userId as string,
      type: item.module_type,
      action: "skipped",
      progress: 0,
      course_module_id: item.course_module_id ,
    };
    try {
      const response = await skipResource(reqParams);
      if (response) {
        closeDialog();

        toast({
          variant: "default",

          title: t("success"),

          description: t("skip_resource"),
        });

        if (type === "module") {
          const updatedItem = { ...item, resource_status: 1 };
          const updatedData = moduleData.map((resource) =>
            resource.instance === item.instance ? updatedItem : resource
          );
          setModuleData(updatedData);
        } else {
          const updatedItem = { ...item, resource_status: 1 };

          if (folderResource) {
            const updatedData = folderResource.resources.map((resource) =>
              resource.instance === item.instance ? updatedItem : resource
            );
            setFolderResource({
              ...folderResource,
              resources: updatedData as FolderResourceType[],
            });
          }
        }
      }
    } catch (error) {
      closeDialog();
      console.error("Error skipping resource:", error);

      toast({
        variant: "destructive",

        title: t("error"),

        description: t("skip_resource"),
      });
    }
  };

  const handleCancel = () => {
    router.push(`/pages/section-details`);
  };
  return (
    <MainLayout titleText="">
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="w-full flex flex-col items-center  p-4 space-y-4">
        <div className="w-full">
          <Tabs
            defaultValue="resources"
            onValueChange={handleTabChange}
            className=""
          >
            <TabsList className="grid w-full grid-cols-2 h-6  mb-4">
              <TabsTrigger value="resources">
                <div className="flex flex-col items-center">
                  <FilePlus2
                    className={
                      activeTab === "resources"
                        ? "exam-feedback-icon"
                        : "text-black"
                    }
                  />
                  <span
                    className={
                      activeTab === "resources"
                        ? "exam-feedback-icon"
                        : "text-black"
                    }
                  >
                    {t("Resources")}
                  </span>
                  <Separator
                    className={`my-2 ${
                      activeTab === "resources"
                        ? "exam-feedback-icon"
                        : "bg-transparent"
                    }`}
                  />
                </div>
              </TabsTrigger>
              <TabsTrigger value="folders">
                <div className="flex flex-col items-center">
                  <Folders
                    className={
                      activeTab === "folders"
                        ? "exam-feedback-icon"
                        : "text-black"
                    }
                  />
                  <span
                    className={
                      activeTab === "folders"
                        ? "exam-feedback-icon"
                        : "text-black"
                    }
                  >
                    {t("Folders")}
                  </span>
                  <Separator
                    className={`my-2 ${
                      activeTab === "folders"
                        ? "bg-[#00afbb]"
                        : "bg-transparent"
                    }`}
                  />
                </div>
              </TabsTrigger>
            </TabsList>
            <TabsContent value="resources" className="mt-14">
              {!openFolders && (
                <div className="items-center">
                  <Card className="bg-orange-50">
                    <CardHeader className="bg-[#FDB666] text-white rounded-md h-7 place-content-center">
                      <CardTitle>{t("Section Details")}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {moduleData?.length > 0 ? (
                        <div className="flex flex-col space-y-4 mt-4">
                          <Stepper ref={stepperRef} orientation="vertical">
                            {moduleData?.map(
                              (resource: ResoureceModuleType, index) => {
                                const isDisabled = moduleData
                                  .slice(0, index)
                                  .some(
                                    (prevResource) =>
                                      prevResource.progress < 100 &&
                                      prevResource.resource_status !== 1
                                  );
                                return (
                                  <StepperPanel
                                    header={getFileType(
                                      resource.extension,
                                      resource.module_type
                                    )}
                                    key={index}
                                  >
                                    <div
                                      className={`flex flex-col bg-neutral-200 rounded-lg p-4 mb-4 shadow-lg transition-transform transform cursor-pointer ${
                                        isDisabled
                                          ? "opacity-50 cursor-not-allowed pointer-events-none"
                                          : ""
                                      }`}
                                    >
                                      <div className="flex items-center justify-between font-medium text-lg cursor-pointer">
                                        <span className="text-sky-700">
                                          {resource.module_name}
                                        </span>
                                        <span className="ml-auto flex items-center space-x-4">
                                          <span>
                                            {t("Progress")} :{" "}
                                            {resource.progress} %
                                          </span>
                                          <Eye
                                            onClick={() => {
                                              if (!isDisabled) {
                                                handleClickModule(resource);
                                              }
                                            }}
                                          ></Eye>
                                        </span>
                                        {resource.progress < 100 && (
                                          <span className="ml-2 lg:ml-16 cursor-pointer ">
                                            {resource.resource_status === 0 ||
                                            resource.resource_status ===
                                              null ? (
                                              <span title={t("Skip")}>
                                                <IterationCw
                                                  onClick={() => {
                                                    setSelectedItem(resource);
                                                    setOpenSkipModal(true);
                                                    setType("module");
                                                  }}
                                                ></IterationCw>
                                              </span>
                                            ) : (
                                              <div className="px-3 py-1 bg-gray-200 text-gray-700 text-sm italic rounded-full shadow-md text-center">
                                                {t("Skipped")}
                                              </div>
                                            )}
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  </StepperPanel>
                                );
                              }
                            )}
                          </Stepper>
                        </div>
                      ) : (
                        <p className="text-center mt-2">
                          {t("No Subject Details Found")}
                        </p>
                      )}
                    </CardContent>
                  </Card>
                </div>
              )}
            </TabsContent>
            <TabsContent value="folders" className="mt-14">
              <div className="items-center">
                <Card className="bg-orange-50">
                  <CardHeader className="bg-[#FDB666] text-white rounded-md h-7 place-content-center">
                    <CardTitle> {t("Folders")}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {folderData?.length > 0 ? (
                      <div className="flex flex-col space-y-4 mt-4">
                        <Stepper ref={stepperRef} orientation="vertical">
                          {folderData?.map((folder: FolderType, index) => (
                            <StepperPanel key={index}>
                              <div className="flex flex-col h-12rem">
                                <div
                                  className="flex-auto flex items-center font-medium cursor-pointer"
                                  onClick={() => {
                                    openFolder(folder);
                                  }}
                                >
                                  <FileSearch />
                                  <span className="ml-2 text-sky-700">
                                    {folder.folder_name}
                                  </span>
                                  <span className="ml-2">{folder.file}</span>
                                </div>
                              </div>
                            </StepperPanel>
                          ))}
                        </Stepper>
                      </div>
                    ) : (
                      <p className="text-center mt-2">
                        {" "}
                        {t("No Folders Found")}
                      </p>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        {openFolders && (
          <div className="items-center w-full">
            <Card className="bg-orange-50">
              <CardHeader className="bg-[#FDB666] text-white rounded-md h-7 place-content-center">
                <CardTitle>{folderResource?.folder_name}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col space-y-4 mt-4">
                  <Stepper ref={stepperRef} orientation="vertical">
                    {folderResource?.resources.map(
                      (folder: FolderResourceType, index) => {
                        const isDisabled = folderResource.resources
                          .slice(0, index)
                          .some(
                            (resource) =>
                              resource.progress < 100 &&
                              resource.resource_status !== 1
                          );
                        return (
                          <StepperPanel
                            key={index}
                            header={getFileType(
                              folder.extension,
                              folder.module_type
                            )}
                          >
                            <div>
                              <div
                                className={`flex flex-col bg-neutral-200 rounded-lg p-4 mb-4 shadow-lg transition-transform transform cursor-pointer ${
                                  isDisabled
                                    ? "opacity-50 cursor-not-allowed pointer-events-none"
                                    : ""
                                }`}
                                key={index}
                              >
                                <div className="flex-auto flex items-center font-medium">
                                  <span className="flex-grow text-sky-700">
                                    {folder.module_name}
                                  </span>
                                  <span className="ml-auto">
                                    {t("Progress")} : {folder.progress} %
                                  </span>
                                  <span className="ml-10">
                                    <Eye
                                      onClick={() => {
                                        if (!isDisabled) {
                                          handleClickModule(folder);
                                        }
                                      }}
                                    ></Eye>
                                  </span>
                                  {folder.progress < 100 && (
                                    <span className="ml-2 lg:ml-16 cursor-pointer ">
                                      {folder.resource_status === 0 ||
                                      folder.resource_status === null ? (
                                        <span title="skip">
                                          <IterationCw
                                            onClick={() => {
                                              setSelectedItem(folder);
                                              setOpenSkipModal(true);
                                              setType("folder");
                                            }}
                                          ></IterationCw>
                                        </span>
                                      ) : (
                                        <div className="px-3 py-1 bg-gray-200 text-gray-700 text-sm italic rounded-full shadow-md">
                                          {t("Skipped")}
                                        </div>
                                      )}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </StepperPanel>
                        );
                      }
                    )}
                  </Stepper>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
        {viewOpen && (
          <Modal
            title=""
            header=""
            openDialog={viewOpen}
            closeDialog={closeDialog}
            type="max-w-5xl"
          >
            <VideoDialog onCancel={closeDialog} url={url} />
          </Modal>
        )}

        {viewOpenPPT && (
          <Modal
            title=""
            header=""
            openDialog={viewOpenPPT}
            closeDialog={closeDialog}
            type="max-w-5xl"
          >
            <DocumentDialog
              onCancel={closeDialog}
              url={url}
              pageCount={pageCount}
            />
          </Modal>
        )}
        {openSkipModal && (
          <Modal
            title=""
            header=""
            openDialog={openSkipModal}
            closeDialog={closeDialog}
            type="max-w-lg"
          >
            <SkipModal
              closeDialog={closeDialog}
              proceedSkip={() =>
                selectedItem && skipModuleResources(selectedItem)
              }
            ></SkipModal>
          </Modal>
        )}
      </div>
      <div className="flex justify-end mt-4 sticky bottom-2">
        <Button variant="outline" className="rounded-md" onClick={handleCancel}>
          {t("Back")}
        </Button>
      </div>
    </MainLayout>
  );
}
