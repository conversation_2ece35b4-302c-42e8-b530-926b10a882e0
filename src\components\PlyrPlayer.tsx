"use client";

import React, { useEffect, useRef } from 'react';
import Plyr from 'plyr';
import 'plyr/dist/plyr.css';

interface PlyrPlayerProps {
  src: string;
  onProgress?: (data: { played: number; playedSeconds: number; loaded: number; loadedSeconds: number }) => void;
  onStart?: () => void;
  onEnd?: () => void;
  onReady?: () => void;
  width?: string;
  height?: string;
  className?: string;
}

const PlyrPlayer: React.FC<PlyrPlayerProps> = ({
  src,
  onProgress,
  onStart,
  onEnd,
  onReady,
  width = "100%",
  height = "500px",
  className = ""
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<Plyr | null>(null);

  useEffect(() => {
    if (videoRef.current && !playerRef.current) {
      // Initialize Plyr
      playerRef.current = new Plyr(videoRef.current, {
        controls: [
          'play-large',
          'play',
          'progress',
          'current-time',
          'duration',
          'mute',
          'volume',
          'settings',
          'fullscreen'
        ],
        settings: ['quality', 'speed'],
        quality: {
          default: 720,
          options: [1080, 720, 480, 360]
        }
      });

      // Event listeners
      playerRef.current.on('ready', () => {
        onReady?.();
      });

      playerRef.current.on('play', () => {
        onStart?.();
      });

      playerRef.current.on('ended', () => {
        onEnd?.();
      });

      playerRef.current.on('timeupdate', () => {
        const currentTime = playerRef.current?.currentTime || 0;
        const duration = playerRef.current?.duration || 0;
        
        if (duration > 0) {
          onProgress?.({
            played: currentTime / duration,
            playedSeconds: currentTime,
            loaded: 1,
            loadedSeconds: duration
          });
        }
      });
    }

    return () => {
      if (playerRef.current) {
        playerRef.current.destroy();
        playerRef.current = null;
      }
    };
  }, [src, onProgress, onStart, onEnd, onReady]);

  return (
    <div className={className} style={{ width, height }}>
      <video
        ref={videoRef}
        controls
        crossOrigin="anonymous"
        playsInline
        src={src}
        style={{ width: '100%', height: '100%' }}
      />
    </div>
  );
};

export default PlyrPlayer;
