/**
 * Test Google Drive URL conversion
 */

import { getGoogleDriveUrls, processPdfUrl, testGoogleDriveUrl } from './googleDriveUtils';

/**
 * Test your specific Google Drive URL
 */
export function testYourGoogleDriveUrl() {
  const yourUrl = 'https://drive.google.com/file/d/12lmaX8c2Z5JBK8L_l50wbY5l54xZOevN/view?usp=drive_link';
  
  console.log('🧪 Testing your Google Drive URL...\n');
  console.log('Original URL:', yourUrl);
  
  const urls = getGoogleDriveUrls(yourUrl);
  
  if (urls) {
    console.log('\n✅ Successfully extracted file ID:', urls.fileId);
    console.log('\n📋 Available URL formats:');
    console.log('1. Direct Download (for PDF viewers):', urls.directDownload);
    console.log('2. Preview (embeddable):', urls.preview);
    console.log('3. View (Google Drive viewer):', urls.view);
    console.log('4. Embed (for iframes):', urls.embed);
    
    console.log('\n🎯 Recommended for PDF viewer:', urls.directDownload);
    
    return urls;
  } else {
    console.log('❌ Failed to process URL');
    return null;
  }
}

/**
 * Quick test function you can call from anywhere
 */
export function quickTest() {
  return testYourGoogleDriveUrl();
}
