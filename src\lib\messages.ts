
export const ERROR_MESSAGES = {
  already_exists: "User already exist!",
  noExamsMsg: "No exams available!",
  noVideoMsg: "No videos available!",
  noCurrentAffairsMsg: "No current affairs available!",
  noSubjectMsg: "No Subjects available!",
  wrongAnswer:"You answered wrong",
  skippedText:" You skipped this question", 
  noPlanText: " No plans available!",
  exam_time_exeed: "The allotted time for this exam has expired. Your responses will now be automatically submitted!",
  file_size_limit: "Sorry. The selected file size is too large. Up to 1MB size is allowed!",
  service_unreach_msg:"We are currently unable to reach the service. Please try again later!",
  tab_open_alert: "The Exam View is already open in another tab. Please close it before opening a new one!",
  token_not_found: "Please sign in to continue.",
  checkpint_msg: "This video includes checkpoint exams that will appear at different intervals throughout the video!",
  checkpint_file_msg: "This file includes checkpoint exams that will appear at different intervals throughout the video!",
  comments_add: "Failed to add comments!",
  error: "ERROR!",
  skip_resource: "Failed to skip the resource.",
  join_meeting: "Unable to join meeting: No URL or valid meeting ID available",
  course_request: "Failed to request course."
};
export const SUCCESS_MESSAGES = {
  logIn: "You've successfully signed in!",
  signUp: "You've successfully signed up!",
  logout: "You've successfully signed out!",
  exam_submit: "Successfully submitted exam!",
  title: "SUCCESS!",
  profile_update_title:"SUCCESS!",
  profile_update_msg:"profile details updated sucessfully!",
  correctAnswer: "You answered correctly!",
  addPlanResponse: "Subscription added, Waiting for the approval from admin!",
  topicChangeMsg: "Are you sure you want to change the topic?",
  flag_msg: "This question has been flagged for review!",
  flag_remove_msg: "Review flag cleared for this question!",
  reset_link_sent_success: "Reset Password link sent successfully!",
  comments_add: "Your comment has been added successfully and is awaiting admin approval!",
  success: "SUCCESS!",
  progress_complete_msg:"You've completed watching the video. Your progress has been updated!",
  progress_msg: "Your progress has been updated!",
  skip_resource :"Resource skipped successfully",
  link_copied: "Meeting Link copied!",
  id_copied: "Meeting ID copied!",
  passcode_copied: "Meeting Passcode copied!",
  course_request: "Course Requested Successfully"
};
