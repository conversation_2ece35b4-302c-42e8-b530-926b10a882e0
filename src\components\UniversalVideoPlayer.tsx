"use client";

import React, { useState } from 'react';
import ReactPlayer from 'react-player';
import { isGoogleDriveUrl, getGoogleDriveVideoUrls } from '@/lib/googleDriveVideoUtils';
import HTML5VideoPlayer from './HTML5VideoPlayer';

interface UniversalVideoPlayerProps {
  url: string;
  onProgress?: (data: { played: number; playedSeconds: number; loaded: number; loadedSeconds: number }) => void;
  onStart?: () => void;
  onEnd?: () => void;
  onReady?: () => void;
  width?: string;
  height?: string;
  className?: string;
  controls?: boolean;
  playing?: boolean;
  light?: string | boolean;
}

const UniversalVideoPlayer: React.FC<UniversalVideoPlayerProps> = ({
  url,
  onProgress,
  onStart,
  onEnd,
  onReady,
  width = "100%",
  height = "500px",
  className = "",
  controls = true,
  playing = false,
  light = false
}) => {
  const [playerType, setPlayerType] = useState<'reactplayer' | 'html5' | 'iframe'>('reactplayer');
  const [hasError, setHasError] = useState(false);

  // Check if it's a Google Drive URL
  if (isGoogleDriveUrl(url)) {
    const driveUrls = getGoogleDriveVideoUrls(url);
    
    if (!driveUrls) {
      return (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          ❌ Invalid Google Drive URL
        </div>
      );
    }

    return (
      <div className={`relative ${className}`} style={{ width, height }}>
        {/* Player Type Selector */}
        <div className="absolute top-2 left-2 z-10 bg-black bg-opacity-70 rounded p-2">
          <select
            value={playerType}
            onChange={(e) => setPlayerType(e.target.value as any)}
            className="text-xs bg-transparent text-white border border-gray-400 rounded px-1"
          >
            <option value="iframe" className="text-black">Iframe Player</option>
            <option value="html5" className="text-black">HTML5 Player</option>
            <option value="reactplayer" className="text-black">ReactPlayer</option>
          </select>
        </div>

        {/* Iframe Player (Most Compatible) */}
        {playerType === 'iframe' && (
          <div className="relative w-full h-full">
            <iframe
              src={driveUrls.embedUrl}
              width="100%"
              height="100%"
              allow="autoplay; encrypted-media"
              allowFullScreen
              title="Google Drive Video"
              className="rounded-lg"
              onLoad={() => onReady?.()}
            />
            
            {/* Manual controls overlay */}
            <div className="absolute bottom-4 right-4 space-x-2">
              <button
                onClick={() => onStart?.()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
              >
                Mark Started
              </button>
              <button
                onClick={() => onEnd?.()}
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
              >
                Mark Complete
              </button>
            </div>
          </div>
        )}

        {/* HTML5 Player */}
        {playerType === 'html5' && (
          <HTML5VideoPlayer
            src={driveUrls.directStream}
            onProgress={onProgress}
            onStart={onStart}
            onEnd={onEnd}
            onReady={onReady}
            width={width}
            height={height}
            className="w-full h-full"
          />
        )}

        {/* ReactPlayer (Fallback) */}
        {playerType === 'reactplayer' && (
          <ReactPlayer
            url={driveUrls.directStream}
            controls={controls}
            playing={playing}
            light={light}
            width="100%"
            height="100%"
            onProgress={onProgress}
            onStart={onStart}
            onEnded={onEnd}
            onReady={onReady}
            onError={(error) => {
              console.error('ReactPlayer error with Google Drive:', error);
              setHasError(true);
            }}
            className="rounded-lg"
          />
        )}

        {/* Error fallback */}
        {hasError && (
          <div className="absolute inset-0 bg-red-100 border border-red-400 text-red-700 p-4 rounded flex items-center justify-center">
            <div className="text-center">
              <p className="font-semibold">❌ Video Player Error</p>
              <p className="text-sm mt-2">Try switching to Iframe Player</p>
              <button
                onClick={() => {
                  setPlayerType('iframe');
                  setHasError(false);
                }}
                className="mt-2 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
              >
                Switch to Iframe
              </button>
            </div>
          </div>
        )}

        {/* Info overlay */}
        <div className="absolute top-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
          Google Drive • {playerType}
        </div>
      </div>
    );
  }

  // For non-Google Drive URLs, use ReactPlayer
  return (
    <ReactPlayer
      url={url}
      controls={controls}
      playing={playing}
      light={light}
      width={width}
      height={height}
      onProgress={onProgress}
      onStart={onStart}
      onEnded={onEnd}
      onReady={onReady}
      className={className}
    />
  );
};

export default UniversalVideoPlayer;
