import { number, z } from "zod";
import {
  ForgotPasswordFormSchema,
  LoginFormSchema,
  ProfileFormSchema,
  SignUpFormSchema,
} from "./schema/schema";
import { Icon, LucideIcon } from "lucide-react";
import { Url } from "next/dist/shared/lib/router/router";
import type { MomentInput } from "moment";
export type LoginFormType = z.infer<typeof LoginFormSchema>;
export type ForgotPasswordFormType = z.infer<typeof ForgotPasswordFormSchema>;
export type SignupFormType = z.infer<typeof SignUpFormSchema>;
export type ProfileFormType = z.infer<typeof ProfileFormSchema>;
export interface ModalProps {
  title: string;
  children: React.JSX.Element;
  header: string;
  openDialog: boolean;
  closeDialog: (value: boolean) => void;
  footer?: React.ReactNode;
  type?: string;
  closeIcon?: boolean;
}

export interface CourseResourceData {
  external_url?: string;
  module_type?: string;
  name: string;
  type: string;
  file: Url;
  // file: string;
  instance?: string;
  url?: string;
}

export interface ResourceViewerProps {
  resourceData: ViewResourcePageType;
  fileType: string;
  closeDialog: () => void;
}

export interface ImagerViewerProps {
  url: string;
}
export interface LoginReturn {
  data: {};
  error: string;
  session: {};
}
export interface SendResetPasswordReturn {
  data: {};
  error: string;
  session: {};
}
export interface LoginUserData {
  id: string;
  last_sign_in_at: string;
  email?: string;
  user_metadata: {
    first_name: string;
    last_name: string;
    phone_number: string;
  };
}
export interface LoginDataReturn {
  app_metadata: {};
  identities: [];
  user_metadata: {
    first_name: string;
    last_name: string;
    phone_number: string;
    avatar_url: string;
    phonenumber1: string;
    email: string;
  };
  id: string;
  last_sign_in_at: string;
  email?: string;
}
export interface ToastType {
  toast: (options: {
    variant?: string;
    title: string;
    description?: string;
  }) => void;
}
export interface ErrorCatch {
  details: string | undefined;
  message: string;
  Error?: string;
}
export interface SessionData {
  access_token: string;
  expires_at: number;
  expires_in: number;
  refresh_token: string;
  token_type: string;
}
export interface profileImageResponse {
  avatar_url?: string;
}

export interface allProfileResponse {
  id: string;
  org_of_role: string;
  email: string;
  first_name: string;
  last_name: string;
  bio: string;
  avatar_url: string;
  phonenumber1: string;
  phonenumber2: string;
}
export interface SignUpRequestType {
  email: string;
  password: string;
  data: {
    first_name: string;
    last_name: string;
    password: string;
    phone_number: string;
  };
}
export interface signUpReturn {
  access_token: string;
  token_type: string;
  user: {
    id: string;
    app_metadata: {
      provider: string;
    };
    user_metadata: {
      email: string;
      first_name: string;
      last_name: string;
      phone_number: string;
    };
    identities: [];
    created_at: string;
    updated_at: string;
  };
}
export interface CourseDetailsRequest {
  course_id: string;
  org_id: string;
}
export interface CourseDetailsResultType {
  length: number;
  course_id: string;
  short_name: string;
  full_name: string;
  summary: string;
  status: string;
  start_date: Date;
  end_date: Date;
  course_resources: [];
  sections: [];
  modules: [];
  category_id: string;
  current_affairs: [];
  course_type: string;
}
export interface courseModules {
  course_id: string;
  course_module_id: string;
  instance: string;
  module_id: string;
  module_name: string;
  module_source: string;
  module_type: string;
  quiz_type: string;
  section_id: string;
}
interface Topic {
  topic: string;
  icon?: React.ComponentType<{ className?: string }>;
  subtopics?: Subtopic[];
}

interface Subtopic {
  icon: React.ComponentType<{ className?: string }>;
  subtopic: string;
  courses: Course[];
}

interface Course {
  course: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface Props {
  topics: Topic[];
}

export interface File {
  instance: string;
  progress: number;
  module_id: string;
  section_id: string;
  time_spent: string;
  is_premium: boolean;
  module_name: string | null;
  module_type: string;
  module_source: string;
  marked_as_done: boolean;
  attempts_remaining: number;
}

export interface Folder {
  files: File[];
  folder_id: string;
  section_id: string;
  folder_desc: string;
  folder_name: string;
}

export interface SectionViewResultType {
  length: number;
  name: string;
  modules: ResoureceModuleType[];
  summary: string;
  course_id: string;
  resources: [];
  section_id: string;
  section_order: number;
  instance: string;
  module_type: string;
  folders: FolderType[];
}
export interface ResoureceModuleType {
  instance: string;
  progress: number;
  course_id: string;
  module_id: string;
  section_id: string;
  time_spent: string;
  module_name: string;
  module_type: string;
  module_source: string;
  marked_as_done: boolean;
  attempts_remaining: number;
  is_premium: boolean;
  extension: string;
  course_module_id: string;
  file_extension: string;
  resource_type: string;
  resource_id: string | null;
  page_count: number;
  resource_url: string;
  external_url: string;
  resource_status: number;
}

export interface Resource {
  id: string;
  url: string;
  name: string;
  type: string;
  org_id: string;
  created_at: string;
  updated_at: string;
}

export interface Module {
  instance: string;
  progress: number;
  course_id: string;
  module_id: string;
  section_id: string;
  time_spent: string;
  module_name: string | null;
  module_type: string;
  module_source: string;
  marked_as_done: boolean;
  attempts_remaining: number;
  is_premium: boolean;
}

export interface SectionDetails {
  name: string;
  folders: Folder[];
  modules: Module[];
  summary: string;
  course_id: string;
  resources: Resource[];
  section_id: string;
  section_order: number;
  is_premium?: boolean;
  course_module_id: string;
}
export interface ViewResourcePageType {
  type: string;
  id: string;
  url: string;
  name: string;
  org_id: string;
  status: string;
  comments: string;
  course_id: string;
  created_at: string;
  updated_at: string;
  approved_by: string;
  description: string;
  approved_date: string;
  module_source: string;
  file: string;
  external_url: string;
  course_module_id: string;
  is_checkpoint_enabled: string;
  page_count?: number;
}

export interface FolderType {
  section_id: string;
  folder_desc: string;
  folder_name: string;
  folder_id: string;
  resources: FolderResourceType[];
  file: string;
  instance: string;
}
export interface FolderResourceType {
  instance: string;
  progress: number;
  module_id: string;
  is_premium: boolean;
  section_id: string;
  time_spent: string;
  module_name: string;
  module_type: string;
  module_source: string;
  marked_as_done: boolean;
  course_module_id: string;
  attempts_remaining: number;
  course_id: string;
  extension: string;
  file_extension: string;
  resource_type: string;
  resource_id: string;
  page_count: number;
  resource_url: string;
  external_url: string;
  resource_status: number;
}
export interface ExamQuestionType {
  name: string;
  duration: number;
  end_time: string;
  course_id: string;
  pass_mark: number;
  quiz_type: string;
  main_topic: string;
  section_id: string;
  start_time: string;
  total_mark: number;
  category_id: string;
  description: string;
  section_name: string;
  category_name: string;
  quest_answers: [
    {
      name: string;
      status: string;
      answers: [
        {
          slot: number;
          answer: string;
          org_id: string;
          fraction: number;
          answer_id: string;
          ans_format: string;
          created_at: string;
          updated_at: string;
          answer_type: string;
          question_id: string;
        }
      ];
      penalty: number;
      quiz_id: string;
      question_id: string;
      default_mark: number;
      question_slot: number;
      question_text: string;
      question_type: string;
    }
  ];
  allowed_attempts: number;
  course_full_name: string;
  num_of_questions: number;
  course_short_name: string;
  eq_weightage_marks: number;
  is_equal_weightage: false;
}

export interface QuizRequest {
  course_id: string;
  org_id: string;
  user_id: string;
  quizes_of_course_data: {
    quiz_type: string[];
  };
}

export interface QuizResponse {
  id: string;
  name: string;
  org_id: string;
  duration: number;
  end_time: string;
  course_id: string;
  pass_mark: number;
  quiz_type: string;
  created_at: string;
  is_premium: boolean;
  main_topic: string;
  start_time: string;
  total_mark: number;
  updated_at: string;
  description: string;
  allowed_attempts: number;
  num_of_questions: number;
  penalty_available: boolean;
  publish_status: string;
}

export interface ErrorType {
  code: string;
  details: string;
  hint: string;
  message: string;
}

export interface AttemptedQuizRequest {
  course_id: string;
  org_id: string;
  user_id: string;
  type_of_quiz: string;
}

export interface AttemptedQuizResponse {
  name: string;
  org_id: string;
  result: string;
  quiz_id: string;
  user_id: string;
  duration: number;
  end_time: string;
  pass_mark: number;
  quiz_type: string;
  main_topic: string;
  start_time: string;
  total_mark: number;
  description: string;
  mark_per_quiz: number;
  quiz_attempt_id: string;
  allowed_attempts: number;
  num_of_questions: number;
  max_attempts_done: number;
  penalty_available: boolean;
  attempts_remaining: number;
  attempt_ids_of_quiz: AttemptedIdsOfQuiz[];
  passed_date: string;
}

export interface AttemptedIdsOfQuiz {
  date: string;
  attempt: number;
  attempt_id: string;
  sum_grades: number;
  end_time: string;
}
[];
export interface ExamViewRequest {
  org_id: string;
  quiz_id: string;
  user_id: string;
  user_start_time: Date;
}
export interface ExamViewResult {
  status: string;
  quiz_attempt_id: string;
  quizId: string;
}
export interface ExamQuestionsResult {
  name: string;
  duration: number;
  end_time: string;
  course_id: string;
  pass_mark: number;
  quiz_type: string;
  main_topic: string;
  section_id: string;
  start_time: string;
  total_mark: number;
  category_id: string;
  description: string;
  section_name: string;
  category_name: string;
  quest_answers: QuestionAnswerType[];
  allowed_attempts: number;
  course_full_name: string;
  num_of_questions: number;
  course_short_name: string;
  eq_weightage_marks: number;
  is_equal_weightage: false;
}
export interface QuestionAnswerType {
  answers: AnswerType[];
  default_mark: number;
  name: string;
  penalty: number;
  question_id: string;
  question_text: string;
  question_type: string;
  quiz_id: string;
  status: string;
  isFlagged?: boolean;
}
export interface AnswerType {
  ans_format: string;
  answer: string;
  answer_id: string;
  answer_type: string;
  created_at: string;
  fraction: number;
  org_id: string;
  question_id: string;
  slot: number;
  updated_at: string;
  answer_marked: boolean;
}
export interface SubmitAnswerType {
  question_id: string;
  question_with_options: string;
  response_summary: string[];
  selected_answer_ids: string[];
}
export interface SubmitQuizType {
  org_id?: string;
  quiz_id?: string;
  user_id?: string;
  quiz_attempt_id?: string;
  submit_datas?: SubmitAnswerType[];
}
export interface SubmitQuizResponseType {
  status?: string;
}
export interface ExamReviewType {
  name: string;
  duration: number;
  end_time: string;
  pass_mark: number;
  main_topic: string;
  start_time: string;
  total_mark: number;
  description: string;
  scored_mark: number;
  num_of_questions: number;
  skipped_ans_count: number;
  wrong_answer_count: number;
  correct_answer_count: number;
  quest_answers?: QuestAnswersType[];
  category_wise_summary: CategoryWiseSummaryType[];
}
export interface QuestAnswersType {
  mark: number;
  name: string;
  penalty: number;
  quiz_id: string;
  question_id: string;
  default_mark: number;
  question_slot: number;
  question_text: string;
  question_type: string;
  answers: ReviewAnswersType[];
  selected_answer_ids: string[];
}
export interface ReviewAnswersType {
  slot: number;
  answer: string;
  org_id: string;
  fraction: number;
  answer_id: string;
  ans_format: string;
  created_at: string;
  updated_at: string;
  answer_type: string;
  question_id: string;
  is_correct_answer: boolean;
}
export interface currentAffairsResponse {
  content: string;
  course_id: string;
  created_at: string;
  id: string;
  img_url: string;
  is_deleted: boolean;
  month: string;
  org_id: string;
  publish_date: string;
  publish_status: string;
  title: string;
  type: string;
  updated_at: string;
}
export interface QuizGradeResponse {
  status: string;
  sum_grades: string;
  quiz_start_time: string;
}
export interface SubmitAnswerType {
  quiz_id: string;
  quiz_attempt_id: string;
}
export interface AttemptedQuizType {
  course_id: string;
  org_id: string;
  user_id: string;
  type_of_quiz?: string;
}
export interface AttemptedQuizResponse {
  name: string;
  result: string;
  duration: number;
  end_time: string;
  pass_mark: number;
  start_time: string;
  total_mark: number;
  description: string;
  attempt_ids_of_quiz: AttemptedIdsOfQuiz[];
  passed_date: string;
}
[];
export interface CategoryWiseSummaryType {
  skipped_ans_count: number;
  wrong_answer_count: number;
  correct_answer_count: number;
  question_category_id: string;
  question_category_name: string;
}
export interface UserOrganizationReturn {
  user_id: string;
  org_id: string;
  org_name: string;
}

export interface RankListRequest {
  org_id: string;
  course_id: string;
  quiz_type_filter: string;
}
export interface ExamRankListRequest {
  org_id: string;
  quiz_id: string;
  quiz_type_filter: string;
}

export interface RankListResponse {
  status: string;
  rank_list: RankListData[];
}

export interface RankListData {
  rank: number;
  user_id: string;
  attempts: number;
  user_name: string;
  avatar_url: string;
  total_sum_grades: number;
  wrong_answers_count?: number;
  correct_answers_count?: number;
  skipped_answers_count?: number;
}
export interface ExamViewRequest {
  org_id: string;
  quiz_id: string;
  user_id: string;
  user_start_time: Date;
}
export interface ExamViewResult {
  status: string;
  quiz_attempt_id: string;
  quizId: string;
}
export interface ExamQuestionsResult {
  name: string;
  duration: number;
  end_time: string;
  course_id: string;
  pass_mark: number;
  quiz_type: string;
  main_topic: string;
  section_id: string;
  start_time: string;
  total_mark: number;
  category_id: string;
  description: string;
  section_name: string;
  category_name: string;
  quest_answers: QuestionAnswerType[];
  allowed_attempts: number;
  course_full_name: string;
  num_of_questions: number;
  course_short_name: string;
  eq_weightage_marks: number;
  is_equal_weightage: false;
}
export interface QuestionAnswerType {
  answers: AnswerType[];
  default_mark: number;
  name: string;
  penalty: number;
  question_id: string;
  question_text: string;
  question_type: string;
  quiz_id: string;
  status: string;
  isFlagged?: boolean;
}
export interface AnswerType {
  ans_format: string;
  answer: string;
  answer_id: string;
  answer_type: string;
  created_at: string;
  fraction: number;
  org_id: string;
  question_id: string;
  slot: number;
  updated_at: string;
  answer_marked: boolean;
}
export interface submitAnswerType {
  question_id: string;
  question_with_options: string;
  response_summary: string[];
  selected_answer_ids: string[];
}
export interface submitQuizType {
  org_id?: string;
  quiz_id?: string;
  user_id?: string;
  quiz_attempt_id?: string;
  submit_datas?: submitAnswerType[];
}
export interface submitQuizResponseType {
  status?: string;
}
export interface examReviewType {
  name: string;
  duration: number;
  end_time: string;
  pass_mark: number;
  main_topic: string;
  start_time: string;
  total_mark: number;
  description: string;
  scored_mark: string;
  num_of_questions: number;
  skipped_ans_count: number;
  wrong_answer_count: number;
  correct_answer_count: number;
  quest_answers?: QuestAnswersType[];
}
export interface QuestAnswersType {
  mark: number;
  name: string;
  penalty: number;
  quiz_id: string;
  question_id: string;
  default_mark: number;
  question_slot: number;
  question_text: string;
  question_type: string;
  answers: ReviewAnswersType[];
  selected_answer_ids: string[];
}
export interface ReviewAnswersType {
  slot: number;
  answer: string;
  org_id: string;
  fraction: number;
  answer_id: string;
  ans_format: string;
  created_at: string;
  updated_at: string;
  answer_type: string;
  question_id: string;
  is_correct_answer: boolean;
}
export interface currentAffairsResponse {
  content: string;
  course_id: string;
  created_at: string;
  id: string;
  img_url: string;
  is_deleted: boolean;
  month: string;
  org_id: string;
  publish_date: string;
  publish_status: string;
  title: string;
  type: string;
  updated_at: string;
}
export interface Plan {
  price: number;
  status: string;
  currency: string;
  plan_name: string;
  is_expired: boolean;
  subscription_valid_to: string;
  subscription_valid_from: string;
}

export interface subscriptionReportResponse {
  result: {
    pending_plan_lists_by_user: Plan[];
    purchased_plan_lists_by_user: Plan[];
  };
  status: string;
}
export interface subscriptionReportType {
  pending_plan_lists_by_user: Plan[];
  purchased_plan_lists_by_user: Plan[];
}
export interface EditProfileFormType {
  profile_datas: ProfileData[];
}
interface ProfileData {
  id: string;
  first_name: string;
  last_name: string;
  avatar_url: string;
  phonenumber1: string;
}
export interface EditProfileReturn {
  status: string;
}
export interface UserDetails {
  id: string;
  org_of_role: string;
  email: string;
  first_name: string;
  last_name: string;
  bio: string | null;
  avatar_url: string;
  phonenumber1: string;
  phonenumber2: string;
  created_at: string;
  roles: string[];
}
export interface UserOrganizationReturn {
  user_id: string;
  org_id: string;
  org_name: string;
}

export interface TopicsRequest {
  org_id: string;
  filter_data: number;
}

export interface CourseData {
  course_id: string;
  course_name: string;
  full_name?: string;
  short_name?: string;
}
export interface TopicsData {
  courses?: CourseData[];
  name: string;
  id: string;
  org_id: string;
  children?: TopicsData[] | null;
  is_premium: boolean;
  description: string;
  publish_status: string;
}
export interface CommentsResponse {
  activity_type: string;
  id: string;
  name: string;
  type: string;
  message: string;
  subject: string;
  children: CommentsResponse[] | null;
  avatar_url: string;
  created_at: string;
  user_id?: string;
  status?: string;
  role_name?: string;
}

export interface GetCommentsRequest{
  instance_id: string,
  org_id: string,
  activity_type: string,
}

export interface CheckPoint {
  status: string;
  attended: boolean;
  org_id: string;
  sequence: number;
  created_at: string;
  created_by: string;
  start_page: number;
  start_time: string;
  updated_at: string;
  updated_by: string;
  instance_id: string;
  module_name: string;
  is_mandatory: boolean;
  checkpoint_id: string;
  module_type_id: string;
  checkpoint_name: string;
  checkpoint_type: string;
  course_module_id: string;
  instance_end_time: string;
}
export interface CheckPointsResponse {
  status: string;
  check_points: CheckPoint[];
}
export interface CheckExamDetailsRequest {
  user_id: string;
  org_id: string;
  checkpoint_id: string;
  user_start_time: string;
}

export interface CheckExamDetailsResponse {
  status: string;
  questions: CheckExamDetailsQuestion[];
  quiz_attempt_id: string;
  module_session_id: string;
}

export interface CheckExamDetailsQuestion {
  name: string;
  duration: number;
  end_time: string;
  pass_mark: number;
  quiz_type: string;
  main_topic: string;
  start_time: string;
  total_mark: number;
  description: string;
  quest_answers: CheckExamDetailsQuestAnswer[];
  penalty: number;
  quiz_id: string;
  question_id: string;
  default_mark: number;
  question_slot: number | null;
  question_text: string;
  question_type: string;
}

export interface CheckExamDetailsQuestAnswer {
  name: string;
  answers: CheckExamDetailsAnswer[];
  penalty: number;
  quiz_id: string;
  question_id: string;
  default_mark: number;
  question_slot: number | null;
  question_text: string;
  question_type: string;
}

export interface CheckExamDetailsAnswer {
  slot: number;
  answer: string;
  org_id: string;
  fraction: number;
  answer_id: string;
  ans_format: string;
  created_at: string;
  updated_at: string;
  answer_type: string;
  question_id: string;
}

export interface EndCheckPointResponse {
  result: string;
  status: string;
}
export interface SubscriptionRequestType {
  org_id: string;
  user_id: string;
}
export interface SubscriptionReturnType {
  result: SubscriptionResultType[];
  status: string;
}
export interface SubscriptionResultType {
  id: string;
  name: string;
  price: number;
  org_id: string;
  status: string;
  currency: string;
  valid_to: string;
  valid_from: string;
  description: string;
  subscription_type: string;
  is_high_level_user: boolean;
  is_user_subscribed: boolean;
  user_purchase_date: null;
  user_payment_status: null;
  subscription_frequency: string;
  subscription_plan_status: string;
  subscription_usage_status: string;
  user_subscription_end_date: null;
  user_subscription_start_date: null;
}
export interface AddSubscriptionResult {
  status: string;
  user_subscription_id: string;
  user_subscription_purchase_id: string;
}
export interface AddSubscriptionRequest {
  org_id: string;
  plan_id: string;
  subscription_plan_for_user_data?: { payment_method: string };
  user_id?: string;
}
export interface SubscriptionStatusResult {
  result: string;
  status: string;
}
export interface ResourceList {
  course_name: string;
  resources: CourseResources[];
}
export interface CourseResources {
  name: string;
  type: string;
  description: string;
  resource_id: string;
}
export interface CourseResourceReturn {
  result: ResourceList[];
  status: string;
}
export interface AllResourceRequest {
  org_id: string;
  course_id: string;
  user_id: string;
}
export interface AllResourcesResult {
  result: AllResourceResponse[];
}

// export interface AllResourceResponse {
//   summary: string;
//   course_name: string;
//   course_short_name: string;
//   is_course_expired: boolean;
//   resource_intro_data: [];
//   course_id: string;
//   resources: ResourcesType[];
// }
export interface AllResourceResponse {
  quiz_type: string;
  course_id: string;
  course_name: string;
  course_short_name: string;
  external_url: string;
  file_extension: string;
  is_course_expired: boolean;
  is_part_of_plan: boolean;
  page_count: string | number;
  resource_id: string;
  resource_name: string;
  resource_type: string;
  resource_url: string;
  summary: string;
  valid_to: string;
  video_length: string;
  thumbnail_url?: string;
  is_checkpoint_enabled: boolean;
  section_order: number;
  module_order: number;
  progress: number;
  course_module_id: string;
  resource_status: number;
  section_name: string;
}

export interface insertNotificationRequest {
  org_id: string;
  user_id: string;
  notification_data: {
    device_token: string;
    status: string;
  };
}

export interface SubjectProgressType {
  subject: string;
  completed: number;
  ongoing: number;
}

export interface SubjectMarksType {
  subject: string;
  marks: number;
}
export interface CategoryWiseProgressType {
  name: string;
  value: number;
}

export interface RecentActivitiesType {
  Icon: typeof Icon;
  text: string;
  time: string;
  color: string;
}

export interface AssignmentsType {
  id: number;
  title: string;
  duration: string;
  thumbnail: string;
  subject: string;
  views: string;
}
export interface AddCommentRequest {
  comment_data: {
    subject: string;
    message: string;
    type: string;
    parent_id?: string | null;
    activity_type?: string;
  };
  instance_id: string;
  user_id: string;
}
export interface GetCommentLIkeRequest {
  org_id: string;
  comment_data: {
    activity_type?: string;
  };
  instance_id: string;
  user_id: string;
}
export interface AddCommentResponse {
  status: string;
  comment_id: string;
}
export interface UserStatisticsReq {
  user_id: string;
  course_id?: string | null;
  org_id: string;
}

export interface CategoryStatsReqType {
  org_id: string;
  user_id: string;
}
export interface UserStatisticsResponse {
  "User enrolled course statistics Details": Statistics[];
}
export interface Statistics {
  length?: number;
  course_id: string;
  time_spent: string | number;
  course_name: string;
  resource_id: string;
  total_marks: number | null;
  achievements: number;
  resource_name: string;
  percent_completed: number;
}
[];

export interface CourseStatsGraphType {
  labels: string[];
  datasets: [
    {
      data: number[];
      label: string;
      borderColor: string;
      backgroundColor: string;
      borderWidth: 2;
    }
  ];
}

export interface RecentActivity {
  time_spent: string;
  course_name: string;
  resource_id: string;
  resource_name: string;
  updated_hrs_ago: string;
}

export interface CourseModuleRecentActivities {
  "Course Module Recent Activities": RecentActivity[];
}
export interface CourseProgress {
  result: CourseProgressResult[];
  status: string;
}
export interface CourseProgressResult {
  attended_at: string;
  course_name: string;
  current_page: number;
  current_time: string;
  progress_percent: number;
  resource_name: string;
  time_spent: null;
  total_length: string;
  total_progress: number;
  user_email: string;
  user_id: string;
  user_name: string;
  section_name: string;
}
[];

export interface NotificationRequest {
  user_id: string;
  org_id: string;
}

export interface NotificationResponse {
  course: string;
  created_at: string;
  is_read: boolean;
  messaage_text: string;
  status: string;
  target_id: string;
}
[];

export interface CategorySummaryResultType {
  course_id: string;
  instance_ids: string[];
  user_id: string;
}

export interface PerformanceResult {
  performance_summary: PerformanceSummary;
}

export interface PerformanceSummary {
  question_category: {
    details: {
      category_id: string;
      correct_answers: number;
      marks_obtained: number;
      total_questions: number;
    };
    name: string;
  }[];
}
export interface CategoryPerformanceResultType {
  performance_summary: CategoryPerformanceType;
}

export interface CategoryPerformanceType {
  question_category: {
    category_id: string;
    category_name: string;
    correct_answers: number;
    marks_obtained: number;
    total_questions: number;
  }[];
}
export interface CourseProgressRequestType {
  course_id: string;
  instance_id: string;
  org_id: string;
  progress_data: {
    progress: string;
    time_spent?: string;
  };
  user_id: string;
}

export interface GetCourseProgressRequest {
  course_id: string;
  instance_ids: string[];
  user_id: string;
}

export interface GetCourseProgressResponse {
  progress: number;
  time_spent: string;
  instance_id: string;
  current_page: number;
  current_point: string;
  marked_as_done: boolean;
}
[];

export interface CourseStats {
  course_name: string;
  valuetype: string;
  value: number;
  course_id?: string;
  section_name: string;
}

export interface CheckpointResult {
  id: string;
  name: string;
  status: string;
  start_time: string;
}

export interface SummaryStatistics {
  title: string;
  value: number | string;
  trend: string;
  gradient: string;
  icon: any;
  textColor: string;
}
export interface ProgressPieData {
  name: string;
  value: string;
}
export interface MarksPerResource {
  progress: string;
  time_spent: string;
  totalMarks: number;
  resource_id: string;
  achievements: null;
  resource_name: string;
  resource_type: string;
}

export interface UserStatistics {
  quiz_type: string;
  resource_type: string;
  resource_name: string;
  course_id: string;
  time_spent: string | number;
  course_name: string;
  totalMarks: number;
  achievements: number;
  progress: number;
  section_name: string;
}
[];

export interface customisesLabelPros {
  cx: number;
  cy: number;
  midAngle: number;
  innerRadius: number;
  outerRadius: number;
  percent: number;
}
export interface BreadcrumbItem {
  screen_name: string;
  child_screens: InnerItem;
}

export interface InnerItem {
  name: string;
  path: string;
}
export interface AssignOrg {
  org_id: string;
  role_id: string;
  user_ids: string[];
}

export interface AssignOrgReturn {
  status: string;
}

export interface UpdateDocumentRequest {
  course_id: string;
  instance_id: string;
  org_id: string;
  progress_data: {
    current_page: number;
  };
  user_id: string;
}

export interface GetSessionReport {
  course_id: string;
  course_module_id: string;
  user_id?: string | null;
}

export interface GetSessionReportResponse {
  status: string;
  session_data: {
    modules: {
      checkpoints: GetSessionCheckpoint[];
    }[];

    course_id: string;
    organisation: string;
    course_fullname: string;
    course_shortname: string;
  }[];
}
export interface GetSessionCheckpoint {
  sequence: number;
  sessions: {
    result: string;
    user_id: string;
    lastname: string;
    quiz_name: string;
    first_name: string;
    instance_id: string;
    session_end_time: string;
    session_start_time: string;
    instance_attempt_id: string;
    session_attempt_number: number;
  }[];
}
export interface SentEmailRequest {
  mail_user: string;
  mail_subject: string;
  mail_content: string;
}

export interface SentEmailResponse {
  status: string;
  message: string;
}

export interface SkipVideoRequest {
  org_id: string;
  course_id: string;
  resource_id: string;
  user_id: string;
  type: string;
  action: string;
  progress: number;
  course_module_id: string;
}

export interface SkipVideoResponse {
  status: boolean;
}

export interface ConfigSettingsResponse {
  org_id: string;
  subjects: {
    subjects: SubjectConfig;
  };
  course_id: string;
  resources: ResourceConfig[];
  current_affairs: CurrentAffairConfig;
}

export interface SubjectConfig {
  subjects: {
    subject_id: string;
    subject_name: string;
  }[];
  no_of_items: number;
  component_type: string;
}

export interface ResourceConfig {
  resources: {
    resource_id: string;
    resource_name: string;
    thumbnail_url: string;
  }[];
  no_of_items: number;
  resource_type: string;
}
export interface CurrentAffairConfig {
  no_of_items: number;
  component_type: string;
}
export interface LiveClassReturn {
  org_id: string;
  course_id: string | null;
}
export interface Meeting {
  id: string;
  platform: string;
  meetingId: string;
  meetingUrl: string;
  passcode?: string;
  course: string;
  startTime: string; // ISO format
  endTime: string;
}
export interface LiveClassResponse {
  status: string;
  end_date: string;
  passcode: string;
  course_id: string;
  meeting_id: string;
  start_date: string;
  meeting_url: string;
  meeting_type: string;
  meeting_status: string;
}
[];
export interface CustomBrandingDetails {
  org_id: string;
  theme_name: string;
  main_logo: string;
  app_logo: string;
  banner_image: string;
  app_background_color: string;
  top_bar_color: string;
  font_family: string;
  button_primary_color: string;
  button_primary_text_color: string;
  button_secondary_color: string;
  button_secondary_text_color: string;
  button_dismiss_bg_color: string;
  button_dismiss_text_color: string;
  button_info_background_color: string;
  button_info_text_color: string;
  toast_success_color: string;
  toast_error_color: string;
  toast_warning_color: string;
  toast_info_color: string;
  navigation_text_color: string;
  footer_background_color: string;
  footer_text_color: string;
  footer_text: string;
  welcome_text: string;
  valid_from: string;
  valid_to: string;
  top_bar_background_color: string;
  navbar_text_color: string;
  navbar_text_color_hover: string;
  font_base_size: string;
  navbar_background_color: string;
  font_color: string;
}
[];

export interface UpdateMeetingDetailsRequest {
  org_id: string | null;
  activity_type: string;
  screen_name: string;
  action_details: string;
  target_id: string | null;
  session_id: string | null;
  action_comment: string;
  user_agent: string;
  log_source: string;
  log_result: string;
  user_id: string;
}
export interface UserLogResponse {
  status: string;
  activity_log_id: string;
}
export interface CoursesParams {
  is_premium?: boolean;
  id: string;
  course_id?: string;
  org_id?: string;
  category_id?: string;
  category_name: string;
  category_publish_status: string;
  short_name: string;
  full_name?: string;
  duration: string;
  status: string;
  description: string;
  format: string;
  start_date: moment.MomentInput;
  end_date: moment.MomentInput;
  is_expired?: boolean;
  is_user_enrolled: boolean;
  is_duplicate_course?: boolean;
  no_of_enrollments: number;
  parent_course_id: string;
  course_type: string;
  visibility: boolean;
  summary: string;
}
[];

export interface CourseRequestType {
  org_id: string;
  course_id: string;
  requested_by: string;
  resource_id: string | null;
  resource_type: string | null;
  status_notes: string | null;
  updated_plan_id: string| null;
  request_status?: string;
}

export interface CourseRequestResponse {
  status: string;
  error: string;
  
  requests: [];
}
