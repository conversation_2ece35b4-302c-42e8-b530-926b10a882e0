"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import GoogleDriveVideoPlayer from './GoogleDriveVideoPlayer';
import { getGoogleDriveVideoUrls } from '@/lib/googleDriveVideoUtils';

const GoogleDriveVideoDemo: React.FC = () => {
  const [testUrl, setTestUrl] = useState('');
  const [embedUrl, setEmbedUrl] = useState('');
  const [duration, setDuration] = useState('00:05:00'); // Default 5 minutes
  const [progress, setProgress] = useState(0);
  const [events, setEvents] = useState<string[]>([]);

  const addEvent = (event: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setEvents(prev => [`[${timestamp}] ${event}`, ...prev.slice(0, 9)]);
  };

  const handleTest = () => {
    if (!testUrl.trim()) {
      alert('Please enter a Google Drive URL');
      return;
    }

    const urls = getGoogleDriveVideoUrls(testUrl);
    if (urls) {
      setEmbedUrl(urls.embedUrl);
      addEvent(`Generated embed URL: ${urls.embedUrl}`);
    } else {
      alert('Invalid Google Drive URL');
    }
  };

  const handleProgress = (progressData: any) => {
    setProgress(progressData.played * 100);
    // Only log every 10% to avoid spam
    if (Math.floor(progressData.played * 10) !== Math.floor(progress / 10)) {
      addEvent(`Progress: ${Math.round(progressData.played * 100)}%`);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🎬 Google Drive Video Player Demo</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* URL Input */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Google Drive Video URL:
            </label>
            <input
              type="text"
              value={testUrl}
              onChange={(e) => setTestUrl(e.target.value)}
              placeholder="https://drive.google.com/file/d/YOUR_FILE_ID/view?usp=sharing"
              className="w-full p-3 border rounded-md"
            />
          </div>

          {/* Duration Input */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Video Duration (HH:MM:SS):
            </label>
            <input
              type="text"
              value={duration}
              onChange={(e) => setDuration(e.target.value)}
              placeholder="00:05:00"
              className="w-full p-3 border rounded-md"
            />
          </div>

          <button
            onClick={handleTest}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-md"
          >
            Load Video
          </button>

          {/* Video Player */}
          {embedUrl && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-4">Video Player:</h3>
              <GoogleDriveVideoPlayer
                src={embedUrl}
                width="100%"
                height="400px"
                totalDuration={duration}
                onProgress={handleProgress}
                onStart={() => addEvent('Video started')}
                onEnd={() => addEvent('Video completed')}
                onReady={() => addEvent('Video ready')}
                className="mb-4"
              />
            </div>
          )}

          {/* Progress Display */}
          {embedUrl && (
            <div className="bg-gray-50 p-4 rounded-md">
              <h4 className="font-semibold mb-2">Progress:</h4>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <p className="text-sm text-gray-600 mt-1">{Math.round(progress)}% completed</p>
            </div>
          )}

          {/* Event Log */}
          {events.length > 0 && (
            <div className="bg-gray-50 p-4 rounded-md">
              <h4 className="font-semibold mb-2">Event Log:</h4>
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {events.map((event, index) => (
                  <div key={index} className="text-sm font-mono text-gray-700">
                    {event}
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>📝 How to Use</CardTitle>
        </CardHeader>
        <CardContent className="text-sm space-y-2">
          <p><strong>1. Get Google Drive Video URL:</strong></p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>Upload a video to Google Drive</li>
            <li>Right-click → Share → Change to "Anyone with the link"</li>
            <li>Copy the share link</li>
          </ul>
          
          <p className="mt-4"><strong>2. Features:</strong></p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>✅ Progress tracking with visual progress bar</li>
            <li>✅ Start/Pause/Resume controls</li>
            <li>✅ Skip forward/backward (10 seconds)</li>
            <li>✅ Manual completion marking</li>
            <li>✅ Event logging (onStart, onProgress, onEnd)</li>
            <li>✅ Time display (current/total)</li>
          </ul>

          <p className="mt-4"><strong>3. Integration:</strong></p>
          <div className="bg-gray-100 p-2 rounded font-mono text-xs">
            {`<GoogleDriveVideoPlayer
  src="https://drive.google.com/file/d/FILE_ID/preview"
  totalDuration="00:10:30"
  onProgress={handleProgress}
  onStart={handleStart}
  onEnd={handleEnd}
/>`}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GoogleDriveVideoDemo;
