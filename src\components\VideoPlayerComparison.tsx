"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { isGoogleDriveUrl, getGoogleDriveVideoUrls } from '@/lib/googleDriveVideoUtils';
import UniversalVideoPlayer from './UniversalVideoPlayer';
import HTML5VideoPlayer from './HTML5VideoPlayer';
import ReactPlayer from 'react-player';

const VideoPlayerComparison: React.FC = () => {
  const [testUrl, setTestUrl] = useState('');
  const [playerType, setPlayerType] = useState<'universal' | 'html5' | 'reactplayer' | 'iframe'>('universal');
  const [events, setEvents] = useState<string[]>([]);

  const addEvent = (event: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setEvents(prev => [`[${timestamp}] ${event}`, ...prev.slice(0, 9)]);
  };

  const handleProgress = (data: any) => {
    // Only log every 10% to avoid spam
    const percent = Math.round(data.played * 100);
    if (percent % 10 === 0) {
      addEvent(`Progress: ${percent}%`);
    }
  };

  const getProcessedUrl = () => {
    if (isGoogleDriveUrl(testUrl)) {
      const urls = getGoogleDriveVideoUrls(testUrl);
      return urls ? urls.directStream : testUrl;
    }
    return testUrl;
  };

  const getEmbedUrl = () => {
    if (isGoogleDriveUrl(testUrl)) {
      const urls = getGoogleDriveVideoUrls(testUrl);
      return urls ? urls.embedUrl : testUrl;
    }
    return testUrl;
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🎬 Video Player Comparison for Google Drive</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* URL Input */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Video URL (Google Drive or any other):
            </label>
            <input
              type="text"
              value={testUrl}
              onChange={(e) => setTestUrl(e.target.value)}
              placeholder="https://drive.google.com/file/d/YOUR_FILE_ID/view?usp=sharing"
              className="w-full p-3 border rounded-md"
            />
          </div>

          {/* Player Type Selector */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Select Player Type:
            </label>
            <select
              value={playerType}
              onChange={(e) => setPlayerType(e.target.value as any)}
              className="w-full p-3 border rounded-md"
            >
              <option value="universal">Universal Player (Auto-detect)</option>
              <option value="html5">HTML5 Video Player</option>
              <option value="reactplayer">ReactPlayer</option>
              <option value="iframe">Iframe (Google Drive)</option>
            </select>
          </div>

          {/* URL Info */}
          {testUrl && (
            <div className="bg-gray-50 p-4 rounded-md">
              <h4 className="font-semibold mb-2">URL Analysis:</h4>
              <p className="text-sm"><strong>Is Google Drive:</strong> {isGoogleDriveUrl(testUrl) ? '✅ Yes' : '❌ No'}</p>
              {isGoogleDriveUrl(testUrl) && (
                <>
                  <p className="text-sm"><strong>Direct Stream URL:</strong></p>
                  <code className="text-xs bg-white p-1 rounded block mt-1 break-all">{getProcessedUrl()}</code>
                  <p className="text-sm mt-2"><strong>Embed URL:</strong></p>
                  <code className="text-xs bg-white p-1 rounded block mt-1 break-all">{getEmbedUrl()}</code>
                </>
              )}
            </div>
          )}

          {/* Video Player */}
          {testUrl && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-4">
                Video Player: {playerType.charAt(0).toUpperCase() + playerType.slice(1)}
              </h3>
              
              <div className="bg-black rounded-lg overflow-hidden" style={{ height: '400px' }}>
                {/* Universal Player */}
                {playerType === 'universal' && (
                  <UniversalVideoPlayer
                    url={testUrl}
                    onProgress={handleProgress}
                    onStart={() => addEvent('Video started')}
                    onEnd={() => addEvent('Video ended')}
                    onReady={() => addEvent('Player ready')}
                    width="100%"
                    height="400px"
                  />
                )}

                {/* HTML5 Player */}
                {playerType === 'html5' && (
                  <HTML5VideoPlayer
                    src={getProcessedUrl()}
                    onProgress={handleProgress}
                    onStart={() => addEvent('HTML5 video started')}
                    onEnd={() => addEvent('HTML5 video ended')}
                    onReady={() => addEvent('HTML5 player ready')}
                    width="100%"
                    height="400px"
                  />
                )}

                {/* ReactPlayer */}
                {playerType === 'reactplayer' && (
                  <ReactPlayer
                    url={getProcessedUrl()}
                    controls
                    width="100%"
                    height="400px"
                    onProgress={handleProgress}
                    onStart={() => addEvent('ReactPlayer started')}
                    onEnded={() => addEvent('ReactPlayer ended')}
                    onReady={() => addEvent('ReactPlayer ready')}
                    onError={(error) => addEvent(`ReactPlayer error: ${error}`)}
                  />
                )}

                {/* Iframe */}
                {playerType === 'iframe' && isGoogleDriveUrl(testUrl) && (
                  <div className="relative w-full h-full">
                    <iframe
                      src={getEmbedUrl()}
                      width="100%"
                      height="100%"
                      allow="autoplay; encrypted-media"
                      allowFullScreen
                      title="Google Drive Video"
                      onLoad={() => addEvent('Iframe loaded')}
                    />
                    <div className="absolute bottom-4 right-4 space-x-2">
                      <button
                        onClick={() => addEvent('Manual start triggered')}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                      >
                        Start
                      </button>
                      <button
                        onClick={() => addEvent('Manual complete triggered')}
                        className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
                      >
                        Complete
                      </button>
                    </div>
                  </div>
                )}

                {/* Error for iframe with non-Google Drive */}
                {playerType === 'iframe' && !isGoogleDriveUrl(testUrl) && (
                  <div className="flex items-center justify-center h-full text-white">
                    <p>Iframe player only works with Google Drive URLs</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Event Log */}
          {events.length > 0 && (
            <div className="bg-gray-50 p-4 rounded-md">
              <h4 className="font-semibold mb-2">Event Log:</h4>
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {events.map((event, index) => (
                  <div key={index} className="text-sm font-mono text-gray-700">
                    {event}
                  </div>
                ))}
              </div>
              <button
                onClick={() => setEvents([])}
                className="mt-2 text-sm text-blue-600 hover:text-blue-800"
              >
                Clear Log
              </button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>📝 Player Recommendations</CardTitle>
        </CardHeader>
        <CardContent className="text-sm space-y-3">
          <div>
            <strong>🎯 Universal Player (Recommended):</strong>
            <p>Automatically detects Google Drive URLs and provides multiple player options with fallbacks.</p>
          </div>
          
          <div>
            <strong>🖼️ Iframe Player:</strong>
            <p>Most compatible with Google Drive. Uses Google's native player but requires manual progress tracking.</p>
          </div>
          
          <div>
            <strong>🎥 HTML5 Player:</strong>
            <p>Native browser video player. Works with direct video URLs but may have CORS issues with Google Drive.</p>
          </div>
          
          <div>
            <strong>⚛️ ReactPlayer:</strong>
            <p>Great for YouTube, Vimeo, etc. Limited support for Google Drive URLs.</p>
          </div>

          <div className="bg-blue-50 p-3 rounded border-l-4 border-blue-400">
            <strong>💡 Best Practice:</strong>
            <p>Use the Universal Player component - it automatically chooses the best player for each URL type and provides fallback options.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default VideoPlayerComparison;
