"use client";
import { But<PERSON> } from "@/components/ui/button";
import useComments from "@/hooks/useComments";
import {
  AddCommentRequest,
  LoginUserData,
  ToastType,
  UpdateMeetingDetailsRequest,
} from "@/types";
import React, { useState } from "react";
import { useToast } from "./ui/use-toast";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { KEYS } from "@/lib/keys";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UseLogClass } from "@/hooks/useLog";
import { getLocalStorageItem } from "@/lib/utils";
import { useTranslation } from "next-i18next";
interface CommentsModalProps {
  closeDialog: () => void;
  instanceId: string;
  isReply?: boolean;
  commentId?: string;
  commentType?: string;
  onSuccess?: () => void;
}

export default function CommentsModal({
  closeDialog,
  instanceId,
  isReply,
  commentId,
  commentType,
  onSuccess,
}: CommentsModalProps): React.JSX.Element {
  const { t } = useTranslation("common");
  const [selectedOption, setSelectedOption] = useState("Feedback");
  const [feedback, setFeedback] = useState("");
  const { insertLogDetails } = UseLogClass();
  const { addComments } = useComments();
  const { toast } = useToast() as unknown as ToastType;

  const closeModal = () => {
    closeDialog();
  };

  const submitFeedback = async () => {
    const USER_DATA = localStorage.getItem(KEYS.USER_DETAILS);
    if (USER_DATA) {
      const userInfo = JSON.parse(USER_DATA) as LoginUserData;
      const userId = userInfo.id;

      const reqParams: AddCommentRequest = {
        comment_data: {
          subject: "",
          message: feedback,
          type: isReply === true ? (commentType as string) : selectedOption,
          parent_id: isReply === true ? commentId : null,
          activity_type: "comment",
        },
        instance_id: instanceId,
        user_id: userId,
      };

      try {
        const result = await addComments(reqParams);
        if (result.status === "success") {
          await insertLogDetails(
            "Course_Resource",
            "Add Comment",
            `Comment Added `,
            "SUCCESS",
            commentId as string
          );
          // toast({
          //   variant: "success",
          //   title: SUCCESS_MESSAGES.success,
          //   description: SUCCESS_MESSAGES.comments_add,
          // });
          toast({
            variant: "default",

            title: t("success"),

            description: t("comments_add"),
          });
          if (onSuccess) onSuccess();
          closeModal();
        }
      } catch (error) {
        toast({
          variant: "destructive",

          title: t("error"),

          description: t("comments_add"),
        });
        await insertLogDetails(
          "Course_Resource",
          "Add Comment",
          `Failed To Add Comment  `,
          "ERROR",
          commentId as string
        );
        closeModal();
      }
    }
  };

  return (
    <div className="flex justify-center items-center border border-gray-300 -mt-5 text-[var(--color-font-color)]">
      <div className="bg-white rounded-lg p-4 w-full max-w-md ">
        {/* <p className="text-center text-lg font-semibold">Select option</p> */}
        {(isReply === false || isReply === undefined) && (
          <div className="mb-3 mt-3 ">
            <Select onValueChange={(value) => setSelectedOption(value)}>
              <SelectTrigger className="w-full ">
                <SelectValue placeholder={t("Select option")} />
              </SelectTrigger>
              <SelectContent className="text-[var(--color-font-color)]">
                <SelectItem value="Feedback">{t("Feedback")}</SelectItem>
                <SelectItem value="Suggestion">{t("Suggestion")}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <p className="mb-2">
          {t("Kindly share your")}{" "}
          {isReply === true ? (commentType as string) : t(selectedOption)}{" "}
          {t("about the course")}
        </p>
        <textarea
          className="w-full p-2 bg-[#FFFF] rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#9FC089]"
          placeholder={t("Type here")}
          rows={4}
          onChange={(e) => setFeedback(e.target.value)}
        ></textarea>
        <div className="flex justify-between mt-4">
          <Button
            variant="outline"
            className=" w-1/2 mr-2"
            onClick={closeDialog}
          >
            {t("Cancel")}
          </Button>
          <Button
            className="w-1/2"
            disabled={!feedback.trim()}
            onClick={submitFeedback}
            variant="default"
          >
            {t("Submit")}
          </Button>
        </div>
      </div>
    </div>
  );
}
