/**
 * Google Drive URL utilities for PDF viewing
 */

/**
 * Extract file ID from various Google Drive URL formats
 * @param url - Google Drive URL
 * @returns File ID or null if not found
 */
export function extractGoogleDriveFileId(url: string): string | null {
  const patterns = [
    /\/file\/d\/([a-zA-Z0-9-_]+)/,                    // /file/d/FILE_ID
    /id=([a-zA-Z0-9-_]+)/,                            // ?id=FILE_ID
    /\/open\?id=([a-zA-Z0-9-_]+)/,                    // /open?id=FILE_ID
    /\/document\/d\/([a-zA-Z0-9-_]+)/,                // Google Docs
    /\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/,            // Google Sheets
    /\/presentation\/d\/([a-zA-Z0-9-_]+)/             // Google Slides
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return match[1];
    }
  }
  
  return null;
}

/**
 * Convert Google Drive URL to direct download/view URL
 * @param url - Google Drive URL
 * @returns Direct URL for PDF viewing
 */
export function convertGoogleDriveUrl(url: string): string | null {
  const fileId = extractGoogleDriveFileId(url);
  
  if (!fileId) {
    console.warn('Could not extract file ID from Google Drive URL:', url);
    return null;
  }

  // Return direct download URL that works with PDF viewers
  return `https://drive.google.com/uc?export=download&id=${fileId}`;
}

/**
 * Convert Google Drive URL to embeddable PDF URL
 * @param url - Google Drive URL
 * @returns Embeddable PDF URL
 */
export function convertToEmbeddablePdfUrl(url: string): string | null {
  const fileId = extractGoogleDriveFileId(url);
  
  if (!fileId) {
    return null;
  }

  // Return URL that can be embedded in iframe or used with PDF viewers
  return `https://drive.google.com/file/d/${fileId}/preview`;
}

/**
 * Get multiple URL formats for Google Drive file
 * @param url - Google Drive URL
 * @returns Object with different URL formats
 */
export function getGoogleDriveUrls(url: string) {
  const fileId = extractGoogleDriveFileId(url);
  
  if (!fileId) {
    return null;
  }

  return {
    fileId,
    originalUrl: url,
    directDownload: `https://drive.google.com/uc?export=download&id=${fileId}`,
    preview: `https://drive.google.com/file/d/${fileId}/preview`,
    view: `https://drive.google.com/file/d/${fileId}/view`,
    embed: `https://drive.google.com/file/d/${fileId}/preview`,
    thumbnail: `https://drive.google.com/thumbnail?id=${fileId}&sz=w400-h300`
  };
}

/**
 * Check if URL is a Google Drive URL
 * @param url - URL to check
 * @returns Boolean indicating if it's a Google Drive URL
 */
export function isGoogleDriveUrl(url: string): boolean {
  return url.includes('drive.google.com') || url.includes('docs.google.com');
}

/**
 * Process URL for PDF viewing - converts Google Drive URLs to viewable format
 * @param url - Original URL
 * @returns Processed URL suitable for PDF viewing
 */
export function processPdfUrl(url: string): string {
  if (isGoogleDriveUrl(url)) {
    const convertedUrl = convertGoogleDriveUrl(url);
    if (convertedUrl) {
      console.log('🔄 Converted Google Drive URL:', convertedUrl);
      return convertedUrl;
    }
  }
  
  return url; // Return original URL if not Google Drive or conversion failed
}

/**
 * Test function for your specific Google Drive URL
 */
export function testGoogleDriveUrl() {
  const testUrl = 'https://drive.google.com/file/d/12lmaX8c2Z5JBK8L_l50wbY5l54xZOevN/view?usp=drive_link';
  
  console.log('🧪 Testing Google Drive URL conversion...');
  console.log('Original URL:', testUrl);
  
  const urls = getGoogleDriveUrls(testUrl);
  if (urls) {
    console.log('📁 File ID:', urls.fileId);
    console.log('📥 Direct Download:', urls.directDownload);
    console.log('👁️ Preview URL:', urls.preview);
    console.log('🖼️ Embed URL:', urls.embed);
    
    return urls;
  } else {
    console.log('❌ Failed to extract file ID');
    return null;
  }
}
