"use client";
import React, { useEffect, useState, useCallback } from "react";
import { <PERSON>, Search, RefreshCw, Clock } from "lucide-react";
import { UseNotification } from "@/hooks/useNotification";
import { KEYS } from "@/lib/keys";
import { getLocalStorageItem } from "@/lib/utils";
import { InnerItem, NotificationResponse } from "@/types";
import MainLayout from "../layouts/mainLayout";
import { useTranslation } from "next-i18next";
import { Button } from "@/components/ui/button";
import { useThemeColors } from "@/hooks/useThemeColors";
import NextBreadcrumb from "@/components/breadcrumb";
import getBreadCrumbItems from "@/hooks/useBreadcrumbs";

const NotificationItem = ({
  notification,
  index,
}: {
  notification: NotificationResponse;
  index: number;
}) => {
  // No need for translation in this component

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const past = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);
    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600)
      return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)} days ago`;
    return past.toLocaleDateString();
  };

  // Get notification style based on type and read status
  const getNotificationStyle = () => {
    const colors = [
      {
        bg: "bg-green-50",
        border: "border-green-200",
        text: "text-green-800",
        icon: "text-green-600",
      },
      {
        bg: "bg-blue-50",
        border: "border-blue-200",
        text: "text-blue-800",
        icon: "text-blue-600",
      },
      {
        bg: "bg-yellow-50",
        border: "border-yellow-200",
        text: "text-yellow-800",
        icon: "text-yellow-600",
      },
      {
        bg: "bg-red-50",
        border: "border-red-200",
        text: "text-red-800",
        icon: "text-red-600",
      },
      {
        bg: "bg-indigo-50",
        border: "border-indigo-200",
        text: "text-indigo-800",
        icon: "text-indigo-600",
      },
    ];

    const colorIndex = index % colors.length;
    return colors[colorIndex];
  };

  const styles = getNotificationStyle();

  // Get icon based on notification type
  const getNotificationIcon = () => {
    // This is a simplified version - you can expand based on notification types
    if (index === 0) return <Bell className={`h-5 w-5 ${styles.icon}`} />;
    if (index === 1) return <RefreshCw className={`h-5 w-5 ${styles.icon}`} />;
    if (index === 2) return <Clock className={`h-5 w-5 ${styles.icon}`} />;
    return <Bell className={`h-5 w-5 ${styles.icon}`} />;
  };

  return (
    <div
      className={`${styles.bg} ${styles.border} border rounded-lg p-4 mb-3 hover:shadow-md transition-all duration-200 cursor-pointer relative`}
    >
      <div className="flex items-center gap-3">
        {getNotificationIcon()}
        <div className="flex-1">
          <p className={`font-medium ${styles.text}`}>
            {notification.messaage_text || "No message content"}
          </p>
        
        </div>

         <p className="text-sm text-gray-500 mt-1">
            {getTimeAgo(notification.created_at)}
          </p>
      </div>
    </div>
  );
};

const ShowAllNotification = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [allMessages, setAllMessages] = useState<NotificationResponse[]>([]);
  const [filter, setFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [displayCount, setDisplayCount] = useState(5);
  const { getNotifications } = UseNotification();
  const { t } = useTranslation("common");
  const [breadcrumbItems, setBreadcrumbItems] = useState<InnerItem[]>([]);
  // Apply theme colors
  useEffect(() => {
    setBreadcrumbItems(getBreadCrumbItems("Notification", {}));
  }, []);
  const savedTheme = getLocalStorageItem("theme");
  useThemeColors(savedTheme ?? "light");

  const fetchNotificationData = useCallback(async (): Promise<void> => {
    try {
      let userID = getLocalStorageItem(KEYS.USER_ID) || "";
      const orgID = getLocalStorageItem(KEYS.ORG_ID);
      const response = await getNotifications({
        org_id: orgID as string,
        user_id: userID as string,
      });
      setAllMessages(response);
      setIsLoading(false);
    } catch (error) {
      console.error("Failed to fetch notifications:", error);
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchNotificationData();
  }, []);

  const filteredNotifications = allMessages?.filter((notification) => {
    const matchesSearch = notification.messaage_text
      ?.toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesFilter =
      filter === "all" ||
      (filter === "unread" && !notification.is_read) ||
      (filter === "read" && notification.is_read);
    return matchesSearch && matchesFilter;
  });

  const displayedNotifications = filteredNotifications?.slice(0, displayCount);
  const hasMore = filteredNotifications?.length > displayCount;
  const unreadCount = allMessages?.filter((msg) => !msg.is_read).length;

  const handleShowMore = () => {
    setDisplayCount((prev) => prev + 10);
  };

  return (
    <MainLayout titleText={""}>
      <NextBreadcrumb
        items={breadcrumbItems}
        separator={<span> | </span>}
        containerClasses="flex py-5"
        listClasses="hover:underline mx-2 font-bold"
        capitalizeLinks
      />
      <div className="w-full p-4">
        {/* <h1 className="text-2xl font-bold mb-6">{t("Notification")}</h1> */}
        {/* Enhanced Header */}
        <div className="sticky ">
          <div className="flex flex-col items-center gap-4 sm:flex-row sm:justify-center sm:gap-4">
            <div className="relative w-full max-w-xs">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder={t("Search notifications...")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-button-primary)] focus:border-transparent transition-all duration-200 text-[var(--color-font-color)] text-sm"
              />
            </div>
            {/* <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-button-primary)] focus:border-transparent bg-white min-w-[100px] transition-all duration-200 text-[var(--color-font-color)] text-sm"
            >
              <option value="all">{t("All")}</option>
              <option value="unread">{t("Unread")}</option>
              <option value="read">{t("Read")}</option>
            </select> */}
          </div>
        </div>

        {/* Content Area */}
        <div className=" mx-auto p-4 md:p-6">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="relative">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 border-t-[var(--color-button-primary)]" />
                <Bell className="absolute inset-0 m-auto h-6 w-6 text-[var(--color-button-primary)]" />
              </div>
              <p className="mt-4 text-[var(--color-font-color)] font-medium">
                {t("Loading notifications...")}
              </p>
            </div>
          ) : filteredNotifications?.length > 0 ? (
            <div className="space-y-4">
              {displayedNotifications.map((notification, index) => (
                <NotificationItem
                  key={index}
                  notification={notification}
                  index={index}
                />
              ))}

              {/* Show More button */}
              {hasMore && (
                <div className="flex justify-center pt-6">
                  <Button
                    onClick={handleShowMore}
                    variant="outline"
                    className="px-8 py-3 border-[var(--color-button-primary)] bg-[var(--color-button-primary)] text-[var(--color-button-primary-text)] transition-all duration-200"
                  >
                    {t("Show More")} (
                    {filteredNotifications.length - displayCount}{" "}
                    {t("remaining")})
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="relative inline-block">
                <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mb-6">
                  <Bell className="h-10 w-10 text-gray-400" />
                </div>
                {searchQuery && (
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-[var(--color-toast-warning)] rounded-full flex items-center justify-center">
                    <Search className="h-4 w-4 text-white" />
                  </div>
                )}
              </div>

              <h3 className="text-xl font-semibold text-[var(--color-font-color)] mb-2">
                {searchQuery ? t("No matching notifications") : t("No notifications")}
              </h3>

              <p className="text-gray-500 max-w-md mx-auto leading-relaxed">
                {searchQuery
                  ? `${t("No notifications found matching")} "${searchQuery}". ${t("Try adjusting your search terms.")}`
                  : filter === "all"
                  ? t("You're all caught up! No new notifications at the moment.")
                  : `${t("No notifications found.")}`}
              </p>

              {searchQuery && (
                <Button
                  variant="outline"
                  onClick={() => setSearchQuery("")}
                  className="mt-4"
                >
                  {t("Clear search")}
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default ShowAllNotification;
