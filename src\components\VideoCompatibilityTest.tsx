"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { isGoogleDriveUrl, getGoogleDriveVideoUrls } from '@/lib/googleDriveVideoUtils';

const VideoCompatibilityTest: React.FC = () => {
  const [results, setResults] = useState<any[]>([]);

  const testUrls = [
    {
      name: 'Google Drive Video',
      url: 'https://drive.google.com/file/d/1ABC123DEF456/view?usp=sharing',
      expected: 'iframe'
    },
    {
      name: 'YouTube Video',
      url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      expected: 'ReactPlayer'
    },
    {
      name: 'Vimeo Video',
      url: 'https://vimeo.com/123456789',
      expected: 'ReactPlayer'
    },
    {
      name: 'Direct MP4',
      url: 'https://example.com/video.mp4',
      expected: 'ReactPlayer'
    }
  ];

  const runTests = () => {
    const testResults = testUrls.map(test => {
      const isGoogleDrive = isGoogleDriveUrl(test.url);
      const actualPlayer = isGoogleDrive ? 'iframe' : 'ReactPlayer';
      const passed = actualPlayer === test.expected;
      
      let driveUrls = null;
      if (isGoogleDrive) {
        driveUrls = getGoogleDriveVideoUrls(test.url);
      }
      
      return {
        ...test,
        isGoogleDrive,
        actualPlayer,
        passed,
        driveUrls
      };
    });
    
    setResults(testResults);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🧪 Video Player Compatibility Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            This test verifies that your video player correctly detects different URL types and uses the appropriate player.
          </p>
          
          <button
            onClick={runTests}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
          >
            Run Compatibility Tests
          </button>

          {results.length > 0 && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-4">Test Results:</h3>
              
              <div className="space-y-3">
                {results.map((result, index) => (
                  <div
                    key={index}
                    className={`p-4 rounded-lg border ${
                      result.passed 
                        ? 'bg-green-50 border-green-200' 
                        : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{result.name}</h4>
                      <span className={`px-2 py-1 rounded text-sm ${
                        result.passed 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {result.passed ? '✅ PASS' : '❌ FAIL'}
                      </span>
                    </div>
                    
                    <div className="text-sm space-y-1">
                      <p><strong>URL:</strong> <code className="bg-gray-100 px-1 rounded">{result.url}</code></p>
                      <p><strong>Expected Player:</strong> {result.expected}</p>
                      <p><strong>Actual Player:</strong> {result.actualPlayer}</p>
                      <p><strong>Google Drive URL:</strong> {result.isGoogleDrive ? '✅ Yes' : '❌ No'}</p>
                      
                      {result.driveUrls && (
                        <div className="mt-2">
                          <p><strong>Generated Embed URL:</strong></p>
                          <code className="text-xs bg-gray-100 p-1 rounded block break-all">
                            {result.driveUrls.embedUrl}
                          </code>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              
              {/* Summary */}
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-semibold mb-2">Summary:</h4>
                <p className="text-sm">
                  ✅ Passed: {results.filter(r => r.passed).length} / {results.length} tests
                </p>
                {results.every(r => r.passed) && (
                  <p className="text-green-600 font-medium mt-2">
                    🎉 All tests passed! Your video player correctly handles different URL types.
                  </p>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Implementation Guide */}
      <Card>
        <CardHeader>
          <CardTitle>📋 Implementation Summary</CardTitle>
        </CardHeader>
        <CardContent className="text-sm space-y-3">
          <div>
            <strong>🎯 Problem Solved:</strong>
            <p>ReactPlayer doesn't support Google Drive URLs, so we implemented a hybrid solution.</p>
          </div>
          
          <div>
            <strong>✅ Solution Implemented:</strong>
            <div className="bg-gray-50 p-3 rounded mt-2">
              <code className="text-xs">
{`{isGoogleDriveUrl(fileUrl) ? (
  // Google Drive: Use iframe with progress overlay
  <div className="relative">
    <iframe src={embedUrl} />
    <ProgressOverlay />
  </div>
) : (
  // Other URLs: Use ReactPlayer as normal
  <ReactPlayer url={url} />
)}`}
              </code>
            </div>
          </div>
          
          <div>
            <strong>🚀 Features Added:</strong>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Automatic URL type detection</li>
              <li>Google Drive iframe player with progress tracking</li>
              <li>ReactPlayer for all other video platforms</li>
              <li>Consistent progress tracking for both types</li>
              <li>Course completion integration</li>
              <li>Event logging for analytics</li>
            </ul>
          </div>

          <div>
            <strong>🎬 Supported Platforms:</strong>
            <div className="grid grid-cols-2 gap-4 mt-2">
              <div>
                <p className="font-medium text-green-600">✅ Google Drive (iframe)</p>
                <ul className="text-xs ml-4 space-y-1">
                  <li>• Custom progress tracking</li>
                  <li>• Manual start/complete controls</li>
                  <li>• Visual progress overlay</li>
                </ul>
              </div>
              <div>
                <p className="font-medium text-blue-600">✅ Other Platforms (ReactPlayer)</p>
                <ul className="text-xs ml-4 space-y-1">
                  <li>• YouTube, Vimeo, Twitch</li>
                  <li>• Direct video files (MP4, etc.)</li>
                  <li>• Native ReactPlayer features</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default VideoCompatibilityTest;
